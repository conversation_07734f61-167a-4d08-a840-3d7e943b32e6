<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class PincodeApiTest extends TestCase
{
    /**
     * Test pincode API with valid pincode
     */
    public function test_pincode_api_with_valid_pincode()
    {
        // Mock the external API response
        Http::fake([
            'api.postalpincode.in/*' => Http::response([
                [
                    'Message' => 'Number of pincode(s) found:23',
                    'Status' => 'Success',
                    'PostOffice' => [
                        [
                            'Name' => 'Amandula',
                            'Description' => null,
                            'BranchType' => 'Branch Post Office',
                            'DeliveryStatus' => 'Delivery',
                            'Circle' => 'Chhattisgarh',
                            'District' => 'Janjgir-champa',
                            'Division' => 'Bilaspur',
                            'Region' => 'Raipur',
                            'Block' => 'Sakti',
                            'State' => 'Chattisgarh',
                            'Country' => 'India',
                            'Pincode' => '495689'
                        ]
                    ]
                ]
            ], 200)
        ]);

        $response = $this->get('/api/location/pincode/495689');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'pincode' => '495689',
                         'city' => 'Janjgir-champa',
                         'district' => 'Janjgir-champa',
                         'state' => 'Chhattisgarh', // Should use Circle and fix spelling
                         'area' => 'Amandula',
                         'country' => 'India'
                     ]
                 ]);
    }

    /**
     * Test pincode API with invalid pincode format
     */
    public function test_pincode_api_with_invalid_format()
    {
        $response = $this->get('/api/location/pincode/12345');

        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Invalid pincode format. Please provide a 6-digit pincode.'
                 ]);
    }

    /**
     * Test pincode API with non-numeric pincode
     */
    public function test_pincode_api_with_non_numeric()
    {
        $response = $this->get('/api/location/pincode/abcdef');

        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Invalid pincode format. Please provide a 6-digit pincode.'
                 ]);
    }

    /**
     * Test pincode API when external API fails
     */
    public function test_pincode_api_with_external_api_failure()
    {
        // Mock external API failure
        Http::fake([
            'api.postalpincode.in/*' => Http::response([
                [
                    'Message' => 'No records found',
                    'Status' => 'Error'
                ]
            ], 200)
        ]);

        $response = $this->get('/api/location/pincode/999999');

        // Should fallback to static data or return error
        $response->assertStatus(404)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Location not found for this pincode'
                 ]);
    }

    /**
     * Test pincode API with static data fallback
     */
    public function test_pincode_api_with_static_data()
    {
        // Mock external API failure
        Http::fake([
            'api.postalpincode.in/*' => Http::response([], 500)
        ]);

        // Test with a pincode that should have static data
        $response = $this->get('/api/location/pincode/400001');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => [
                         'pincode' => '400001',
                         'city' => 'Mumbai',
                         'district' => 'Mumbai',
                         'state' => 'Maharashtra',
                         'country' => 'India'
                     ]
                 ]);
    }
}
