<?php $__env->startSection('title', 'Page Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Page Management</h1>
            <p class="text-muted">Manage your website pages and content</p>
        </div>
        <a href="<?php echo e(route('admin.pages.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New Page
        </a>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.pages.index')); ?>" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo e(request('search')); ?>" placeholder="Search pages...">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="published" <?php echo e(request('status') === 'published' ? 'selected' : ''); ?>>Published</option>
                        <option value="draft" <?php echo e(request('status') === 'draft' ? 'selected' : ''); ?>>Draft</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="template" class="form-label">Template</label>
                    <select class="form-select" id="template" name="template">
                        <option value="">All Templates</option>
                        <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>" <?php echo e(request('template') === $key ? 'selected' : ''); ?>><?php echo e($name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="<?php echo e(route('admin.pages.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <form id="bulk-form" method="POST" action="<?php echo e(route('admin.pages.bulk-action')); ?>">
        <?php echo csrf_field(); ?>
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Pages (<?php echo e($pages->total()); ?>)</h5>
                <div class="d-flex gap-2">
                    <select class="form-select form-select-sm" id="bulk-action" style="width: auto;">
                        <option value="">Bulk Actions</option>
                        <option value="publish">Publish</option>
                        <option value="unpublish">Unpublish</option>
                        <option value="delete">Delete</option>
                    </select>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="executeBulkAction()">
                        Apply
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if($pages->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="40">
                                    <input type="checkbox" class="form-check-input" id="select-all">
                                </th>
                                <th>Title</th>
                                <th>Slug</th>
                                <th>Template</th>
                                <th>Status</th>
                                <th>Menu</th>
                                <th>Updated</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input page-checkbox" 
                                           name="pages[]" value="<?php echo e($page->id); ?>">
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo e($page->title); ?></strong>
                                        <?php if($page->excerpt): ?>
                                        <br><small class="text-muted"><?php echo e(Str::limit($page->excerpt, 60)); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <code><?php echo e($page->slug); ?></code>
                                    <br>
                                    <a href="<?php echo e(route('page.show', $page->slug)); ?>" target="_blank" class="small text-primary">
                                        <i class="fas fa-external-link-alt me-1"></i>View
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo e($templates[$page->template] ?? $page->template); ?></span>
                                </td>
                                <td>
                                    <?php if($page->is_published): ?>
                                    <span class="badge bg-success">Published</span>
                                    <?php else: ?>
                                    <span class="badge bg-warning">Draft</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($page->show_in_menu): ?>
                                    <i class="fas fa-check text-success" title="In Menu"></i>
                                    <small class="text-muted">(<?php echo e($page->menu_order); ?>)</small>
                                    <?php else: ?>
                                    <i class="fas fa-times text-muted" title="Not in Menu"></i>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo e($page->updated_at->format('M d, Y')); ?>

                                        <?php if($page->updater): ?>
                                        <br>by <?php echo e($page->updater->name); ?>

                                        <?php endif; ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?php echo e(route('admin.pages.show', $page)); ?>" 
                                           class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.pages.edit', $page)); ?>" 
                                           class="btn btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deletePage(<?php echo e($page->id); ?>)" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No pages found</h5>
                    <p class="text-muted">Create your first page to get started.</p>
                    <a href="<?php echo e(route('admin.pages.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Page
                    </a>
                </div>
                <?php endif; ?>
            </div>
            <?php if($pages->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($pages->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </form>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this page? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="delete-form" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Select all checkbox functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.page-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk actions
function executeBulkAction() {
    const action = document.getElementById('bulk-action').value;
    const checkedBoxes = document.querySelectorAll('.page-checkbox:checked');
    
    if (!action) {
        alert('Please select an action.');
        return;
    }
    
    if (checkedBoxes.length === 0) {
        alert('Please select at least one page.');
        return;
    }
    
    if (action === 'delete') {
        if (!confirm('Are you sure you want to delete the selected pages? This action cannot be undone.')) {
            return;
        }
    }
    
    // Add action to form
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action;
    document.getElementById('bulk-form').appendChild(actionInput);
    
    // Submit form
    document.getElementById('bulk-form').submit();
}

// Delete single page
function deletePage(pageId) {
    if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
        const form = document.getElementById('delete-form');
        form.action = `/admin/pages/${pageId}`;
        
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/admin/pages/index.blade.php ENDPATH**/ ?>