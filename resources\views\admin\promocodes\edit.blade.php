@extends('layouts.admin')

@section('title', 'Edit Promocode')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row align-items-center mb-4">
        <div class="col-12 col-md-6">
            <h1 class="h4 mb-1 text-gray-800">Edit Promocode</h1>
            <p class="text-muted mb-0">Update promocode details and settings</p>
        </div>
        <div class="col-12 col-md-6 mt-2 mt-md-0">
            <div class="d-flex justify-content-md-end">
                <a href="{{ route('admin.promocodes.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Promocodes
                </a>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="card shadow">
        <div class="card-body">
            <form action="{{ route('admin.promocodes.update', $promocode) }}" method="POST">
                @csrf
                @method('PUT')
                
                <!-- Basic Information -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="code" class="form-label">Promocode <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('code') is-invalid @enderror" 
                               id="code" name="code" value="{{ old('code', $promocode->code) }}" 
                               placeholder="e.g., SAVE20" style="text-transform: uppercase;">
                        @error('code')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', $promocode->name) }}" 
                               placeholder="e.g., 20% Off Sale">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" 
                                  placeholder="Brief description of the promocode">{{ old('description', $promocode->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Discount Settings -->
                <h5 class="mb-3">Discount Settings</h5>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="type" class="form-label">Discount Type <span class="text-danger">*</span></label>
                        <select class="form-select @error('type') is-invalid @enderror" id="type" name="type">
                            <option value="percentage" {{ old('type', $promocode->type) == 'percentage' ? 'selected' : '' }}>Percentage</option>
                            <option value="fixed" {{ old('type', $promocode->type) == 'fixed' ? 'selected' : '' }}>Fixed Amount</option>
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4">
                        <label for="value" class="form-label">Discount Value <span class="text-danger">*</span></label>
                        <input type="number" class="form-control @error('value') is-invalid @enderror" 
                               id="value" name="value" value="{{ old('value', $promocode->value) }}" 
                               step="0.01" min="0" placeholder="e.g., 20 or 500">
                        @error('value')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4">
                        <label for="maximum_discount" class="form-label">Maximum Discount (₹)</label>
                        <input type="number" class="form-control @error('maximum_discount') is-invalid @enderror" 
                               id="maximum_discount" name="maximum_discount" 
                               value="{{ old('maximum_discount', $promocode->maximum_discount) }}" 
                               step="0.01" min="0" placeholder="e.g., 1000">
                        @error('maximum_discount')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="minimum_amount" class="form-label">Minimum Order Amount (₹)</label>
                        <input type="number" class="form-control @error('minimum_amount') is-invalid @enderror" 
                               id="minimum_amount" name="minimum_amount" 
                               value="{{ old('minimum_amount', $promocode->minimum_amount) }}" 
                               step="0.01" min="0" placeholder="e.g., 1000">
                        @error('minimum_amount')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Usage Limits -->
                <h5 class="mb-3">Usage Limits</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="usage_limit" class="form-label">Total Usage Limit</label>
                        <input type="number" class="form-control @error('usage_limit') is-invalid @enderror" 
                               id="usage_limit" name="usage_limit" 
                               value="{{ old('usage_limit', $promocode->usage_limit) }}" 
                               min="1" placeholder="e.g., 100">
                        @error('usage_limit')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="usage_limit_per_user" class="form-label">Usage Limit Per User</label>
                        <input type="number" class="form-control @error('usage_limit_per_user') is-invalid @enderror" 
                               id="usage_limit_per_user" name="usage_limit_per_user" 
                               value="{{ old('usage_limit_per_user', $promocode->usage_limit_per_user) }}" 
                               min="1" placeholder="e.g., 1">
                        @error('usage_limit_per_user')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Validity Period -->
                <h5 class="mb-3">Validity Period</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="starts_at" class="form-label">Start Date</label>
                        <input type="datetime-local" class="form-control @error('starts_at') is-invalid @enderror" 
                               id="starts_at" name="starts_at" 
                               value="{{ old('starts_at', $promocode->starts_at ? $promocode->starts_at->format('Y-m-d\TH:i') : '') }}">
                        @error('starts_at')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="expires_at" class="form-label">End Date</label>
                        <input type="datetime-local" class="form-control @error('expires_at') is-invalid @enderror" 
                               id="expires_at" name="expires_at" 
                               value="{{ old('expires_at', $promocode->expires_at ? $promocode->expires_at->format('Y-m-d\TH:i') : '') }}">
                        @error('expires_at')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Settings -->
                <h5 class="mb-3">Settings</h5>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {{ old('is_active', $promocode->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="first_order_only" name="first_order_only" 
                                   {{ old('first_order_only', $promocode->first_order_only) ? 'checked' : '' }}>
                            <label class="form-check-label" for="first_order_only">
                                First Order Only
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2">
                    <a href="{{ route('admin.promocodes.index') }}" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Promocode
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate code from name
    const nameInput = document.getElementById('name');
    const codeInput = document.getElementById('code');
    
    nameInput.addEventListener('input', function() {
        if (!codeInput.value) {
            const code = this.value
                .toUpperCase()
                .replace(/[^A-Z0-9\s]/g, '')
                .replace(/\s+/g, '')
                .substring(0, 20);
            codeInput.value = code;
        }
    });
    
    // Update discount value placeholder based on type
    const typeSelect = document.getElementById('type');
    const valueInput = document.getElementById('value');
    
    function updateValuePlaceholder() {
        if (typeSelect.value === 'percentage') {
            valueInput.placeholder = 'e.g., 20 (for 20%)';
        } else {
            valueInput.placeholder = 'e.g., 500 (for ₹500)';
        }
    }
    
    typeSelect.addEventListener('change', updateValuePlaceholder);
    updateValuePlaceholder();
});
</script>
@endsection
