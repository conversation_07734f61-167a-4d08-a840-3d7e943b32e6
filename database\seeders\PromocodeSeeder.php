<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Promocode;
use Carbon\Carbon;

class PromocodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $promocodes = [
            [
                'code' => 'WELCOME10',
                'name' => 'Welcome Discount',
                'description' => '10% off for new customers',
                'type' => 'percentage',
                'value' => 10.00,
                'minimum_amount' => 1000.00,
                'maximum_discount' => 500.00,
                'usage_limit' => 100,
                'usage_limit_per_user' => 1,
                'is_active' => true,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(3),
                'first_order_only' => true,
            ],
            [
                'code' => 'SAVE500',
                'name' => 'Fixed Discount',
                'description' => 'Flat ₹500 off on orders above ₹5000',
                'type' => 'fixed',
                'value' => 500.00,
                'minimum_amount' => 5000.00,
                'maximum_discount' => null,
                'usage_limit' => 50,
                'usage_limit_per_user' => 2,
                'is_active' => true,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(2),
                'first_order_only' => false,
            ],
            [
                'code' => 'FESTIVE20',
                'name' => 'Festival Special',
                'description' => '20% off on all jewelry',
                'type' => 'percentage',
                'value' => 20.00,
                'minimum_amount' => 2000.00,
                'maximum_discount' => 2000.00,
                'usage_limit' => 200,
                'usage_limit_per_user' => 1,
                'is_active' => true,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addWeeks(2),
                'first_order_only' => false,
            ],
            [
                'code' => 'GOLD15',
                'name' => 'Gold Collection Discount',
                'description' => '15% off on gold jewelry',
                'type' => 'percentage',
                'value' => 15.00,
                'minimum_amount' => 3000.00,
                'maximum_discount' => 1500.00,
                'usage_limit' => null, // unlimited
                'usage_limit_per_user' => 3,
                'is_active' => true,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonth(),
                'first_order_only' => false,
            ],
            [
                'code' => 'EXPIRED',
                'name' => 'Expired Code',
                'description' => 'This code has expired (for testing)',
                'type' => 'percentage',
                'value' => 25.00,
                'minimum_amount' => 1000.00,
                'maximum_discount' => 1000.00,
                'usage_limit' => 10,
                'usage_limit_per_user' => 1,
                'is_active' => true,
                'starts_at' => Carbon::now()->subWeeks(2),
                'expires_at' => Carbon::now()->subWeek(),
                'first_order_only' => false,
            ],
        ];

        foreach ($promocodes as $promocode) {
            Promocode::create($promocode);
        }
    }
}
