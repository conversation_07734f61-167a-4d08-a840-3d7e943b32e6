<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property string $status
 * @property float $total_amount
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property-read \App\Models\User $user
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\OrderItem[] $orderItems
 *
 * @method static \Illuminate\Database\Eloquent\Builder recent()
 * @method static \Illuminate\Database\Eloquent\Builder byStatus(string $status)
 */
class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'user_id',
        'status',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'payment_status',
        'payment_method',
        'payment_transaction_id',
        'payment_gateway_order_id',
        'payment_gateway',
        'payment_details',
        'razorpay_payment_id',
        'razorpay_signature',
        'payment_completed_at',
        'billing_address',
        'shipping_address',
        'shipping_method',
        'tracking_number',
        'courier_service',
        'tracking_url',
        'notes',
        'admin_notes',
        'updated_by',
        'status_history',
        'delivery_confirmation_method',
        'delivered_to',
        'delivery_notes',
        'shipped_at',
        'delivered_at',
        'confirmed_at',
        'processing_at',
        'packed_at',
        'out_for_delivery_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'status_history' => 'array',
        'payment_details' => 'array',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'processing_at' => 'datetime',
        'packed_at' => 'datetime',
        'out_for_delivery_at' => 'datetime',
        'payment_completed_at' => 'datetime',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Helper methods
    public static function generateOrderNumber()
    {
        $prefix = 'ORD-' . date('Y') . '-';
        $lastOrder = static::where('order_number', 'like', $prefix . '%')
            ->orderBy('order_number', 'desc')
            ->first();

        if ($lastOrder) {
            $lastNumber = (int) substr($lastOrder->order_number, strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    public function markAsShipped()
    {
        $this->update([
            'status' => 'shipped',
            'shipped_at' => now(),
        ]);
    }

    public function markAsDelivered()
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    public function getTotalItemsAttribute()
    {
        return $this->orderItems->sum('quantity');
    }

    // Status update methods with tracking
    public function updateStatus($status, $adminUser = null, $notes = null)
    {
        $oldStatus = $this->status;
        $timestamp = now();

        // Update status history
        $statusHistory = $this->status_history ?? [];
        $statusHistory[] = [
            'status' => $status,
            'timestamp' => $timestamp->toISOString(),
            'updated_by' => $adminUser ? $adminUser->name : 'System',
            'notes' => $notes
        ];

        $updateData = [
            'status' => $status,
            'status_history' => $statusHistory,
            'updated_by' => $adminUser ? $adminUser->name : 'System'
        ];

        // Set specific timestamp fields based on status
        switch ($status) {
            case 'confirmed':
                $updateData['confirmed_at'] = $timestamp;
                break;
            case 'processing':
                $updateData['processing_at'] = $timestamp;
                break;
            case 'packed':
                $updateData['packed_at'] = $timestamp;
                break;
            case 'shipped':
                $updateData['shipped_at'] = $timestamp;
                break;
            case 'out_for_delivery':
                $updateData['out_for_delivery_at'] = $timestamp;
                break;
            case 'delivered':
                $updateData['delivered_at'] = $timestamp;
                break;
        }

        if ($notes) {
            $updateData['admin_notes'] = $notes;
        }

        $this->update($updateData);

        return $this;
    }

    public function addTrackingNumber($trackingNumber, $courierService = 'India Post', $adminUser = null)
    {
        $trackingUrl = $this->generateTrackingUrl($trackingNumber, $courierService);

        $this->update([
            'tracking_number' => $trackingNumber,
            'courier_service' => $courierService,
            'tracking_url' => $trackingUrl,
            'updated_by' => $adminUser ? $adminUser->name : 'System'
        ]);

        // Auto update to shipped status if not already
        if (!in_array($this->status, ['shipped', 'out_for_delivery', 'delivered'])) {
            $this->updateStatus('shipped', $adminUser, 'Tracking number added and item shipped');
        }

        return $this;
    }

    public function generateTrackingUrl($trackingNumber, $courierService)
    {
        $urls = [
            'India Post' => "https://www.indiapost.gov.in/_layouts/15/dop.portal.tracking/trackconsignment.aspx?consignmentnumber={$trackingNumber}",
            'Speed Post' => "https://www.indiapost.gov.in/_layouts/15/dop.portal.tracking/trackconsignment.aspx?consignmentnumber={$trackingNumber}",
            'Blue Dart' => "https://www.bluedart.com/web/guest/trackdartresult?trackFor=0&trackNo={$trackingNumber}",
            'DTDC' => "https://www.dtdc.in/tracking/tracking_results.asp?Ttype=awb_no&strCnno={$trackingNumber}",
            'FedEx' => "https://www.fedex.com/fedextrack/?tracknumbers={$trackingNumber}",
            'DHL' => "https://www.dhl.com/in-en/home/<USER>"
        ];

        return $urls[$courierService] ?? $urls['Speed Post'];
    }

    public function getStatusBadgeClass()
    {
        $classes = [
            'pending' => 'bg-warning',
            'confirmed' => 'bg-info',
            'processing' => 'bg-primary',
            'packed' => 'bg-secondary',
            'shipped' => 'bg-success',
            'out_for_delivery' => 'bg-success',
            'delivered' => 'bg-success',
            'cancelled' => 'bg-danger',
            'refunded' => 'bg-dark'
        ];

        return $classes[$this->status] ?? 'bg-secondary';
    }

    public function getStatusDisplayName()
    {
        $names = [
            'pending' => 'Pending',
            'confirmed' => 'Confirmed',
            'processing' => 'Processing',
            'packed' => 'Packed',
            'shipped' => 'Shipped',
            'out_for_delivery' => 'Out for Delivery',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
            'refunded' => 'Refunded'
        ];

        return $names[$this->status] ?? ucfirst($this->status);
    }

    public function getNextPossibleStatuses()
    {
        $transitions = [
            'pending' => ['confirmed', 'cancelled'],
            'confirmed' => ['processing', 'cancelled'],
            'processing' => ['packed', 'cancelled'],
            'packed' => ['shipped'],
            'shipped' => ['out_for_delivery', 'delivered'],
            'out_for_delivery' => ['delivered'],
            'delivered' => [],
            'cancelled' => [],
            'refunded' => []
        ];

        return $transitions[$this->status] ?? [];
    }

    /**
     * Check if order can be tracked
     */
    public function canBeTracked()
    {
        return !empty($this->tracking_number) && in_array($this->status, [
            'shipped', 'out_for_delivery', 'delivered'
        ]);
    }

    /**
     * Get estimated delivery date
     */
    public function getEstimatedDeliveryDate()
    {
        if ($this->delivered_at) {
            return $this->delivered_at;
        }

        if ($this->shipped_at) {
            // Speed Post typically takes 2-5 business days
            $businessDays = $this->courier_service === 'Speed Post' ? 5 : 7;
            return $this->shipped_at->addDays($businessDays);
        }

        if ($this->confirmed_at) {
            // Processing + shipping time
            return $this->confirmed_at->addDays(7);
        }

        return $this->created_at->addDays(10);
    }
}
