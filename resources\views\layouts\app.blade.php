<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="@yield('description', 'ShreeJi - Exquisite Jewelry Collection. Discover beautiful rings, necklaces, earrings and more. Premium quality gold, silver, and diamond jewelry with free shipping.')">
        <meta name="keywords" content="@yield('keywords', 'jewelry, rings, necklaces, earrings, bracelets, gold jewelry, silver jewelry, diamond jewelry, wedding jewelry, engagement rings, fashion jewelry, handcrafted jewelry, Indian jewelry, traditional jewelry, modern jewelry')">
        <meta name="author" content="ShreeJi Jewelry">
        <meta name="robots" content="@yield('robots', 'index, follow')">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- Canonical URL -->
        <link rel="canonical" href="@yield('canonical', request()->url())">

        <!-- Open Graph Meta Tags -->
        <meta property="og:title" content="@yield('og_title', 'ShreeJi - Exquisite Jewelry Collection')">
        <meta property="og:description" content="@yield('og_description', 'ShreeJi - Exquisite Jewelry Collection. Discover beautiful rings, necklaces, earrings and more. Premium quality gold, silver, and diamond jewelry with free shipping.')">
        <meta property="og:image" content="@yield('og_image', asset('images/og-image.jpg'))">
        <meta property="og:url" content="@yield('og_url', request()->url())">
        <meta property="og:type" content="@yield('og_type', 'website')">
        <meta property="og:site_name" content="ShreeJi Jewelry">
        <meta property="og:locale" content="en_IN">

        <!-- Twitter Card Meta Tags -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="@yield('twitter_title', 'ShreeJi - Exquisite Jewelry Collection')">
        <meta name="twitter:description" content="@yield('twitter_description', 'ShreeJi - Exquisite Jewelry Collection. Discover beautiful rings, necklaces, earrings and more.')">
        <meta name="twitter:image" content="@yield('twitter_image', asset('images/twitter-card.jpg'))">

        <!-- Additional SEO Meta Tags -->
        <meta name="theme-color" content="#8B4513">
        <meta name="msapplication-TileColor" content="#8B4513">
        <meta name="application-name" content="ShreeJi Jewelry">

        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
        <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">
        <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon-32x32.png') }}">
        <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon-16x16.png') }}">
        <link rel="manifest" href="{{ asset('site.webmanifest') }}">

        <title>@yield('title', 'ShreeJi - Exquisite Jewelry Collection | Premium Gold, Silver & Diamond Jewelry')</title>

        <!-- Structured Data (JSON-LD) -->
        @stack('structured-data')

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Julius+Sans+One&family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

        <!-- Custom Styles -->
        <style>
            :root {
                --primary-brown: #8B4513;
                --secondary-brown: #A0522D;
                --dark-brown: #654321;
                --light-brown: #D2B48C;
                --primary-cream: #F5F5DC;
                --secondary-cream: #FFF8DC;
                --warm-cream: #FAEBD7;
                --accent-cream: #F0E68C;
                --primary-pink: #8B4513;
                --secondary-pink: #A0522D;
                --white: #FFFFFF;
                --light-gray: #F8F9FA;
                --dark-gray: #6C757D;
                --text-dark: #2C3E50;
                --navbar-height: 70px;
            }

            body {
                font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
                color: var(--text-dark);
                line-height: 1.6;
                padding-top: var(--navbar-height);
                margin: 0;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                overflow-x: hidden;
                width: 100%;
                font-size: 16px;
                font-weight: 400;
            }

            /* Global layout fixes */
            html, body {
                overflow-x: hidden;
                width: 100%;
                max-width: 100%;
            }

            .container, .container-fluid {
                max-width: 100%;
                overflow-x: hidden;
            }

            .row {
                margin-left: 0;
                margin-right: 0;
            }

            /* Mobile layout adjustments */
            @media (max-width: 991.98px) {
                body {
                    overflow-x: hidden;
                }
            }

            /* Safe area support for devices with notches */
            @supports (padding-bottom: env(safe-area-inset-bottom)) {
                @media (max-width: 991.98px) {
                    body {
                        padding-bottom: env(safe-area-inset-bottom, 0px);
                    }
                }
            }

            /* Modern Mobile-First Typography */
            .font-julius {
                font-family: 'Julius Sans One', sans-serif;
                font-weight: 400;
                letter-spacing: 0.5px;
            }

            .font-cursive-bold {
                font-family: 'Poppins', sans-serif;
                font-weight: 700;
                font-style: italic;
                letter-spacing: 0.3px;
            }

            .font-playfair {
                font-family: 'Playfair Display', serif;
                font-weight: 600;
            }

            .font-modern {
                font-family: 'Poppins', 'Inter', sans-serif;
                font-weight: 500;
            }

            .font-body {
                font-family: 'Inter', 'Roboto', sans-serif;
                font-weight: 400;
            }

            /* Mobile-First Typography Sizes */
            @media (max-width: 576px) {
                .brand-text {
                    font-size: 1.5rem !important;
                    line-height: 1.3;
                }

                .tagline-text {
                    font-size: 1rem !important;
                    line-height: 1.4;
                }

                h1 {
                    font-size: 2rem !important;
                    line-height: 1.2;
                }

                h2 {
                    font-size: 1.5rem !important;
                    line-height: 1.3;
                }

                h3 {
                    font-size: 1.25rem !important;
                    line-height: 1.3;
                }

                h4 {
                    font-size: 1.1rem !important;
                    line-height: 1.3;
                }

                h5 {
                    font-size: 1rem !important;
                    line-height: 1.3;
                }

                p, .lead {
                    font-size: 0.95rem !important;
                    line-height: 1.5;
                }
            }

            @media (min-width: 577px) and (max-width: 768px) {
                .brand-text {
                    font-size: 1.75rem !important;
                    line-height: 1.3;
                }

                .tagline-text {
                    font-size: 1.1rem !important;
                    line-height: 1.4;
                }
            }

            @media (min-width: 769px) {
                .brand-text {
                    font-size: 2.25rem !important;
                    line-height: 1.2;
                }

                .tagline-text {
                    font-size: 1.25rem !important;
                    line-height: 1.3;
                }
            }

            .text-primary-brown {
                color: var(--primary-brown) !important;
            }

            .bg-primary-brown {
                background-color: var(--primary-brown) !important;
            }

            .bg-gradient-brown {
                background: linear-gradient(135deg, var(--primary-brown), var(--secondary-brown));
            }

            .bg-gradient-cream {
                background: linear-gradient(135deg, var(--primary-cream), var(--secondary-cream));
            }

            .bg-gradient-pink {
                background: linear-gradient(135deg, var(--primary-brown), var(--secondary-brown));
                color: white;
            }

            .btn-primary-pink {
                background-color: var(--primary-brown);
                border-color: var(--primary-brown);
                color: white;
                font-weight: 500;
                padding: 12px 30px;
                border-radius: 50px;
                transition: all 0.3s ease;
            }

            .btn-primary-pink:hover {
                background-color: var(--secondary-brown);
                border-color: var(--secondary-brown);
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
            }

            .btn-outline-pink {
                border: 2px solid var(--primary-brown);
                color: var(--primary-brown);
                background: transparent;
                font-weight: 500;
                padding: 12px 30px;
                border-radius: 50px;
                transition: all 0.3s ease;
            }

            .btn-outline-pink:hover {
                background-color: var(--primary-brown);
                color: white;
                transform: translateY(-2px);
            }

            .btn-outline-primary-pink {
                border: 2px solid var(--primary-brown);
                color: var(--primary-brown);
                background: transparent;
                font-weight: 500;
                padding: 12px 30px;
                border-radius: 50px;
                transition: all 0.3s ease;
            }

            .btn-outline-primary-pink:hover {
                background-color: var(--primary-brown);
                border-color: var(--primary-brown);
                color: white;
                transform: translateY(-2px);
            }

            /* Navbar brand styling moved to navbar.blade.php to avoid conflicts */

            /* Main Content */
            .main-content {
                flex: 1;
                min-height: calc(100vh - var(--navbar-height));
            }

            /* Card Styles */
            .card {
                border: none;
                border-radius: 20px;
                overflow: hidden;
                transition: all 0.3s ease;
                box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            }

            .card:hover {
                transform: translateY(-10px);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            }

            .product-card {
                position: relative;
                overflow: hidden;
            }

            .product-card .card-img-top {
                transition: all 0.3s ease;
                height: 250px;
                object-fit: cover;
            }

            .product-card:hover .card-img-top {
                transform: scale(1.05);
            }

            /* Removed product overlay - keeping only subtle zoom effect */

            /* Section Styles */
            .section-title {
                font-family: 'Playfair Display', serif;
                font-size: 3rem;
                font-weight: 600;
                color: var(--text-dark);
                text-align: center;
                margin-bottom: 3rem;
                position: relative;
            }

            .section-title::after {
                content: '';
                position: absolute;
                width: 80px;
                height: 3px;
                background: linear-gradient(90deg, var(--primary-pink), var(--soft-gold));
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
            }

            /* Hero Section */
            .hero-section {
                background: linear-gradient(135deg, rgba(255, 20, 147, 0.1), rgba(255, 105, 180, 0.1));
                min-height: 80vh;
                display: flex;
                align-items: center;
                padding: 2rem 0;
            }

            /* Footer */
            .footer {
                background: linear-gradient(135deg, var(--primary-brown), var(--dark-brown));
                color: var(--primary-cream);
                padding: 60px 0 30px;
                margin-top: auto;
            }

            .footer h5 {
                color: var(--accent-cream);
                font-family: 'Playfair Display', serif;
                margin-bottom: 20px;
                font-size: 1.1rem;
            }

            .footer a {
                color: var(--secondary-cream);
                text-decoration: none;
                transition: all 0.3s ease;
                display: inline-block;
            }

            .footer a:hover {
                color: var(--warm-cream);
                transform: translateX(3px);
            }

            /* Newsletter Section */
            .newsletter-section {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                padding: 2rem;
                backdrop-filter: blur(10px);
            }

            .newsletter-form .input-group {
                max-width: 400px;
                margin: 0 auto;
            }

            .newsletter-form .form-control {
                border-radius: 50px 0 0 50px;
                border: none;
                padding: 12px 20px;
                background: rgba(255, 255, 255, 0.9);
            }

            .newsletter-form .btn {
                border-radius: 0 50px 50px 0;
                padding: 12px 25px;
                border: none;
            }

            /* Footer Links */
            .footer-links li {
                margin-bottom: 8px;
            }

            .footer-links a {
                font-size: 0.9rem;
                padding: 2px 0;
            }

            .footer-contact li {
                margin-bottom: 10px;
                font-size: 0.9rem;
                display: flex;
                align-items: flex-start;
            }

            .footer-contact i {
                margin-top: 2px;
                color: var(--accent-cream);
                flex-shrink: 0;
            }

            /* Social Icons */
            .social-icons {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }

            .social-icons a {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                background: var(--secondary-brown);
                color: var(--primary-cream);
                border-radius: 50%;
                transition: all 0.3s ease;
                font-size: 1rem;
            }

            .social-icons a:hover {
                background: var(--accent-cream);
                transform: translateY(-3px);
                color: var(--primary-brown);
            }

            /* Footer Bottom */
            .footer-bottom {
                margin-top: 2rem;
            }

            .footer-divider {
                border-color: rgba(255, 255, 255, 0.2);
                margin: 2rem 0 1.5rem;
            }

            .footer-legal-links {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;
                justify-content: center;
            }

            .footer-legal-links a {
                font-size: 0.85rem;
                padding: 0.25rem 0;
            }

            /* Mobile First Responsive Design */

            /* Extra Small devices (phones, 576px and down) */
            @media (max-width: 575.98px) {
                :root {
                    --navbar-height: 70px;
                }

                body {
                    padding-top: 70px;
                }

                .section-title {
                    font-size: 1.75rem;
                    margin-bottom: 2rem;
                }

                .hero-section {
                    min-height: 70vh;
                    padding: 1rem 0;
                }

                .hero-section h1 {
                    font-size: 2rem !important;
                    line-height: 1.2;
                    margin-bottom: 1rem !important;
                }

                .hero-section .lead {
                    font-size: 1rem;
                    margin-bottom: 1.5rem !important;
                }

                .btn-primary-pink,
                .btn-outline-pink {
                    padding: 10px 20px;
                    font-size: 0.9rem;
                }

                .card-body {
                    padding: 1rem;
                }

                .product-card .card-img-top {
                    height: 200px;
                }

                /* Hide floating cards on mobile */
                .position-absolute.top-0,
                .position-absolute.bottom-0 {
                    display: none;
                }

                /* Stack stats vertically on very small screens */
                .hero-section .row.mt-5 .col-4 {
                    text-align: center;
                    margin-bottom: 1rem;
                }

                .hero-section .row.mt-5 .col-4 h3 {
                    font-size: 1.5rem;
                }

                /* Footer Mobile Styles */
                .footer {
                    padding: 40px 0 20px;
                }

                .newsletter-section {
                    padding: 1.5rem 1rem;
                    margin-bottom: 2rem;
                }

                .newsletter-section h4 {
                    font-size: 1.25rem;
                }

                .newsletter-form .input-group {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .newsletter-form .form-control,
                .newsletter-form .btn {
                    border-radius: 50px;
                    width: 100%;
                }

                .footer h5 {
                    font-size: 1rem;
                    margin-bottom: 15px;
                }

                .footer-links a,
                .footer-contact li {
                    font-size: 0.85rem;
                }

                .footer-contact li {
                    margin-bottom: 8px;
                }

                .social-icons {
                    justify-content: center;
                    margin-top: 1rem;
                }

                .footer-legal-links {
                    justify-content: center;
                    gap: 0.75rem;
                }

                .footer-legal-links a {
                    font-size: 0.8rem;
                }

                .footer-bottom {
                    text-align: center;
                }

                .footer-divider {
                    margin: 1.5rem 0 1rem;
                }
            }

            /* Small devices (landscape phones, 576px and up) */
            @media (min-width: 576px) and (max-width: 767.98px) {
                :root {
                    --navbar-height: 70px;
                }

                body {
                    padding-top: 70px;
                }

                .section-title {
                    font-size: 2rem;
                    margin-bottom: 2.5rem;
                }

                .hero-section h1 {
                    font-size: 2.5rem !important;
                }

                .product-card .card-img-top {
                    height: 220px;
                }

                /* Footer Small Device Styles */
                .newsletter-form .input-group {
                    flex-direction: row;
                }

                .newsletter-form .form-control {
                    border-radius: 50px 0 0 50px;
                }

                .newsletter-form .btn {
                    border-radius: 0 50px 50px 0;
                }

                .footer-legal-links {
                    justify-content: center;
                }
            }

            /* Medium devices (tablets, 768px and up) */
            @media (min-width: 768px) and (max-width: 991.98px) {
                :root {
                    --navbar-height: 70px;
                }

                body {
                    padding-top: 70px;
                }

                /* Navbar brand sizing handled in navbar.blade.php */

                .section-title {
                    font-size: 2.5rem;
                }

                .hero-section h1 {
                    font-size: 3rem !important;
                }

                /* Footer Medium Device Styles */
                .footer {
                    padding: 50px 0 25px;
                }

                .footer-legal-links {
                    justify-content: flex-end;
                }
            }

            /* Large devices (desktops, 992px and up) */
            @media (min-width: 992px) and (max-width: 1199.98px) {
                .container {
                    max-width: 960px;
                }

                .hero-section h1 {
                    font-size: 3.5rem !important;
                }
            }

            /* Extra large devices (large desktops, 1200px and up) */
            @media (min-width: 1200px) and (max-width: 1399.98px) {
                .container {
                    max-width: 1140px;
                }
            }

            /* XXL devices (larger desktops, 1400px and up) - 14inch laptops */
            @media (min-width: 1400px) {
                .container {
                    max-width: 1320px;
                }

                .hero-section {
                    min-height: 85vh;
                }

                .section-title {
                    font-size: 3.5rem;
                }

                .hero-section h1 {
                    font-size: 4rem !important;
                }
            }

            /* Mobile-First Specific Styles */

            /* Hero Section Mobile Improvements */
            .hero-content {
                text-align: center;
            }

            /* Collections Mobile Improvements */
            @media (max-width: 576px) {
                .section-title {
                    font-size: 2rem;
                    margin-bottom: 1rem;
                }

                .product-card .card-img-top {
                    height: 200px;
                }

                .product-card .card-body {
                    padding: 1rem 0.75rem;
                }

                .product-card .card-title {
                    font-size: 1.1rem;
                    margin-bottom: 0.5rem;
                }

                .product-card .card-text {
                    font-size: 0.85rem;
                    margin-bottom: 0.75rem;
                }

                .btn-lg {
                    padding: 0.75rem 1.5rem;
                    font-size: 1rem;
                }

                /* Featured Products Mobile Spacing */
                .py-5 {
                    padding-top: 2rem !important;
                    padding-bottom: 2rem !important;
                }
            }

            /* Small tablets and large phones */
            @media (min-width: 576px) and (max-width: 768px) {
                .product-card .card-img-top {
                    height: 220px;
                }
            }

            /* Better mobile product overlay */
            @media (max-width: 768px) {
                .product-overlay {
                    opacity: 1;
                    background: rgba(0, 0, 0, 0.3);
                }

                .product-overlay .btn {
                    font-size: 0.85rem;
                    padding: 0.5rem 1rem;
                }
            }

            @media (min-width: 992px) {
                .hero-content {
                    text-align: left;
                }
            }

            .hero-main-image {
                max-height: 400px;
                object-fit: cover;
                width: 100%;
            }

            @media (min-width: 768px) {
                .hero-main-image {
                    max-height: 500px;
                }
            }

            @media (min-width: 1200px) {
                .hero-main-image {
                    max-height: 600px;
                }
            }

            /* Floating Cards */
            .floating-card .card {
                width: 120px;
                animation: float 3s ease-in-out infinite;
            }

            .floating-card-1 {
                animation-delay: 0s;
            }

            .floating-card-2 {
                animation-delay: 1.5s;
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px) translateX(-50%); }
                50% { transform: translateY(-10px) translateX(-50%); }
            }

            /* Stats Section */
            .stats-section {
                margin-top: 2rem;
            }

            @media (min-width: 992px) {
                .stats-section {
                    margin-top: 3rem;
                }
            }

            .stat-number {
                font-size: 1.5rem;
            }

            @media (min-width: 576px) {
                .stat-number {
                    font-size: 2rem;
                }
            }

            @media (min-width: 992px) {
                .stat-number {
                    font-size: 2.5rem;
                }
            }

            .stat-label {
                font-size: 0.8rem;
                display: block;
                margin-top: 0.25rem;
            }

            @media (min-width: 576px) {
                .stat-label {
                    font-size: 0.9rem;
                }
            }

            /* Testimonials Mobile Improvements */
            .testimonial-card {
                transition: all 0.3s ease;
            }

            .testimonial-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
            }

            .testimonial-text {
                font-size: 0.95rem;
                line-height: 1.6;
            }

            @media (min-width: 768px) {
                .testimonial-text {
                    font-size: 1rem;
                }
            }

            .customer-avatar {
                width: 45px !important;
                height: 45px !important;
            }

            @media (min-width: 768px) {
                .customer-avatar {
                    width: 50px !important;
                    height: 50px !important;
                }
            }

            .customer-name {
                font-size: 0.9rem;
            }

            .customer-location {
                font-size: 0.8rem;
            }

            /* Touch-friendly improvements */
            @media (max-width: 991.98px) {
                .btn {
                    min-height: 44px;
                    padding: 12px 24px;
                }

                .card-body {
                    padding: 1.25rem;
                }

                /* Improve tap targets */
                .nav-link,
                .dropdown-item {
                    min-height: 44px;
                    display: flex;
                    align-items: center;
                }
            }

            /* Loading and Performance */
            .hero-main-image,
            .card-img-top {
                loading: lazy;
            }

            /* Accessibility improvements */
            @media (prefers-reduced-motion: reduce) {
                .floating-card .card,
                .card:hover,
                .testimonial-card:hover,
                .product-card:hover {
                    animation: none;
                    transform: none;
                }
            }
        </style>

        @stack('styles')
    </head>
    <body>
        <!-- Navigation -->
        @include('layouts.navbar')

        <!-- Main Content -->
        <main class="main-content">
            @yield('content')
        </main>

        <!-- Footer -->
        @include('layouts.footer')



        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
        
        <!-- Custom JavaScript -->
        <script>

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const navbarHeight = document.querySelector('.navbar').offsetHeight;
                        const targetPosition = target.offsetTop - navbarHeight;
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Cart and Wishlist functionality
            function updateCartCount(count = null) {
                if (count !== null) {
                    // Update with provided count
                    const cartBadges = document.querySelectorAll('.cart-count');
                    cartBadges.forEach(badge => {
                        badge.textContent = count;
                    });
                } else {
                    // Fetch current count from API
                    fetch('{{ route("api.cart.count") }}')
                        .then(response => response.json())
                        .then(data => {
                            const cartBadges = document.querySelectorAll('.cart-count');
                            cartBadges.forEach(badge => {
                                badge.textContent = data.count;
                            });
                        })
                        .catch(error => {
                            console.error('Error updating cart count:', error);
                        });
                }
            }



            // Enhanced notification system for add to cart
            function showEnhancedNotification(message, type, options = {}) {
                // Remove any existing enhanced notifications
                document.querySelectorAll('.enhanced-notification').forEach(notification => {
                    notification.remove();
                });

                const notification = document.createElement('div');
                notification.className = `enhanced-notification position-fixed`;
                notification.style.cssText = `
                    top: 100px;
                    right: 20px;
                    z-index: 9999;
                    min-width: 350px;
                    max-width: 400px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
                    border-left: 4px solid ${type === 'success' ? '#28a745' : '#dc3545'};
                    animation: slideInRight 0.3s ease;
                `;

                const iconClass = type === 'success' ? 'fas fa-check-circle text-success' : 'fas fa-exclamation-triangle text-danger';
                const actionButtons = options.showActions ? `
                    <div class="d-flex gap-2 mt-3">
                        <button class="btn btn-outline-primary btn-sm flex-fill" onclick="window.location.href='{{ route('collections') }}'">
                            <i class="fas fa-arrow-left me-1"></i>Continue Shopping
                        </button>
                        <button class="btn btn-primary btn-sm flex-fill" onclick="window.location.href='{{ route('cart') }}'">
                            <i class="fas fa-shopping-cart me-1"></i>View Cart
                        </button>
                    </div>
                ` : '';

                notification.innerHTML = `
                    <div class="p-3">
                        <div class="d-flex align-items-start">
                            <i class="${iconClass} me-3" style="font-size: 1.2rem; margin-top: 2px;"></i>
                            <div class="flex-grow-1">
                                <div class="fw-semibold mb-1">${type === 'success' ? 'Success!' : 'Error!'}</div>
                                <div class="text-muted small">${message}</div>
                                ${actionButtons}
                            </div>
                            <button type="button" class="btn-close ms-2" onclick="this.closest('.enhanced-notification').remove()"></button>
                        </div>
                    </div>
                `;

                document.body.appendChild(notification);

                // No auto-hide - user must manually close the notification
            }

            // Add CSS animations
            if (!document.getElementById('enhanced-notification-styles')) {
                const style = document.createElement('style');
                style.id = 'enhanced-notification-styles';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOutRight {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            // Initialize counts on page load
            document.addEventListener('DOMContentLoaded', function() {
                updateCartCount();
            });
        </script>

        @stack('scripts')
    </body>
</html>
