<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

/**
 * @property int $id
 * @property string $name
 * @property string $email
 * @property string $phone
 * @property string $role
 * @property float $total_spent
 * @property int $reward_points
 * @property string $membership_level
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Order[] $orders
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\CartItem[] $cartItems

 */
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'date_of_birth',
        'gender',
        'avatar',
        'bio',
        'timezone',
        'language',
        'status',
        'role',
        'preferences',
        'total_spent',
        'reward_points',
        'membership_level',
        'login_count',
        'last_login_at',
        'last_login_ip',
        'email_verified_at',
        'phone_verified_at',
        'is_mobile_verified',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'password' => 'hashed',
        'date_of_birth' => 'date',
        'preferences' => 'array',
        'total_spent' => 'decimal:2',
        'last_login_at' => 'datetime',
        'is_mobile_verified' => 'boolean',
    ];

    // Relationships
    public function orders()
    {
        return $this->hasMany(\App\Models\Order::class);
    }

    public function cartItems()
    {
        return $this->hasMany(\App\Models\CartItem::class);
    }



    // Accessors & Mutators
    public function getPreferencesAttribute($value)
    {
        $preferences = json_decode($value, true);
        return is_array($preferences) ? $preferences : [];
    }

    // Helper methods
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isVip()
    {
        return $this->membership_level === 'vip';
    }

    public function getFullNameAttribute()
    {
        return $this->name;
    }

    public function addRewardPoints($points)
    {
        $this->increment('reward_points', $points);
        $this->updateMembershipLevel();
    }

    public function updateMembershipLevel()
    {
        if ($this->total_spent >= 200000) {
            $this->membership_level = 'vip';
        } elseif ($this->total_spent >= 100000) {
            $this->membership_level = 'gold';
        } elseif ($this->total_spent >= 50000) {
            $this->membership_level = 'silver';
        } else {
            $this->membership_level = 'regular';
        }
        $this->save();
    }

    // Mobile Authentication Methods

    /**
     * Find user by phone number
     */
    public static function findByPhone($phone)
    {
        return static::where('phone', $phone)->first();
    }

    // Removed authentication-related methods for guest-only experience

    /**
     * Get formatted phone number for display
     */
    public function getFormattedPhoneAttribute()
    {
        if (!$this->phone) {
            return null;
        }

        $phone = preg_replace('/[^0-9]/', '', $this->phone);

        if (strlen($phone) === 10) {
            return '+91-' . substr($phone, 0, 5) . '-' . substr($phone, 5);
        }

        return $this->phone;
    }

    /**
     * Update login tracking
     */
    public function updateLoginTracking($ipAddress = null)
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ipAddress,
            'login_count' => $this->login_count + 1,
        ]);
    }

    /**
     * Check if user can set password (post-purchase option)
     */
    public function canSetPassword()
    {
        return $this->isMobileOnly() && $this->orders()->exists();
    }
}
