<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with('category')->active()->inStock();

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('sku', 'like', "%{$searchTerm}%");
            });
        }

        // Filter by price range
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price') && $request->max_price) {
            $query->where('price', '<=', $request->max_price);
        }

        // Filter by metal type
        if ($request->has('metal_type') && $request->metal_type) {
            $query->where('metal_type', $request->metal_type);
        }

        // Sort functionality
        $sortBy = $request->get('sort', 'name');
        $sortOrder = $request->get('order', 'asc');

        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate(12);
        $categories = Category::active()->ordered()->get();

        return view('collections', compact('products', 'categories'));
    }

    public function show($slug)
    {
        $product = Product::with(['category', 'approvedReviews.user'])
            ->where('slug', $slug)
            ->active()
            ->firstOrFail();

        // Track recently viewed product
        $this->trackRecentlyViewed($product->id);

        // Get related products with intelligent matching
        $relatedProducts = $this->getRelatedProducts($product);

        // Get reviews with pagination
        $reviews = $product->approvedReviews()
            ->with('user')
            ->latest()
            ->paginate(5);

        return view('product.detail', compact('product', 'relatedProducts', 'reviews'));
    }

    private function getRelatedProducts($product)
    {
        // Priority 1: Same category products
        $sameCategoryProducts = Product::with('category')
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->active()
            ->inStock()
            ->inRandomOrder()
            ->limit(2)
            ->get();

        // Priority 2: Same metal type (if available)
        $sameMetalProducts = collect();
        if ($product->metal_type) {
            $sameMetalProducts = Product::with('category')
                ->where('metal_type', $product->metal_type)
                ->where('id', '!=', $product->id)
                ->whereNotIn('id', $sameCategoryProducts->pluck('id'))
                ->active()
                ->inStock()
                ->inRandomOrder()
                ->limit(1)
                ->get();
        }

        // Priority 3: Similar price range
        $priceMin = $product->price * 0.7; // 30% lower
        $priceMax = $product->price * 1.3; // 30% higher
        $similarPriceProducts = Product::with('category')
            ->whereBetween('price', [$priceMin, $priceMax])
            ->where('id', '!=', $product->id)
            ->whereNotIn('id', $sameCategoryProducts->pluck('id'))
            ->whereNotIn('id', $sameMetalProducts->pluck('id'))
            ->active()
            ->inStock()
            ->inRandomOrder()
            ->limit(1)
            ->get();

        // Combine all related products
        $relatedProducts = $sameCategoryProducts
            ->merge($sameMetalProducts)
            ->merge($similarPriceProducts);

        // If we still don't have 4 products, fill with random products
        if ($relatedProducts->count() < 4) {
            $additionalProducts = Product::with('category')
                ->where('id', '!=', $product->id)
                ->whereNotIn('id', $relatedProducts->pluck('id'))
                ->active()
                ->inStock()
                ->inRandomOrder()
                ->limit(4 - $relatedProducts->count())
                ->get();

            $relatedProducts = $relatedProducts->merge($additionalProducts);
        }

        return $relatedProducts->take(4);
    }

    public function category($categorySlug)
    {
        $category = Category::where('slug', $categorySlug)->active()->firstOrFail();

        $products = Product::with('category')
            ->where('category_id', $category->id)
            ->active()
            ->inStock()
            ->paginate(12);

        $categories = Category::active()->ordered()->get();

        return view('collections', compact('products', 'category', 'categories'));
    }

    public function search(Request $request)
    {
        $searchTerm = $request->get('q', '');
        $products = collect();
        $categories = Category::active()->ordered()->get();

        if (strlen($searchTerm) >= 2) {
            $products = Product::with('category')
                ->where(function($query) use ($searchTerm) {
                    $query->where('name', 'like', "%{$searchTerm}%")
                          ->orWhere('description', 'like', "%{$searchTerm}%")
                          ->orWhere('sku', 'like', "%{$searchTerm}%")
                          ->orWhere('metal_type', 'like', "%{$searchTerm}%")
                          ->orWhere('stone_type', 'like', "%{$searchTerm}%");
                })
                ->active()
                ->inStock()
                ->paginate(12);
        }

        return view('collections', compact('products', 'searchTerm', 'categories'));
    }

    public function featured()
    {
        $products = Product::with('category')->featured()->active()->inStock()->limit(8)->get();
        return response()->json($products);
    }

    /**
     * Track recently viewed products in session
     */
    private function trackRecentlyViewed($productId)
    {
        $recentlyViewed = session()->get('recently_viewed', []);

        // Remove the product if it already exists to avoid duplicates
        $recentlyViewed = array_filter($recentlyViewed, function($id) use ($productId) {
            return $id != $productId;
        });

        // Add the product to the beginning of the array
        array_unshift($recentlyViewed, $productId);

        // Keep only the last 8 products
        $recentlyViewed = array_slice($recentlyViewed, 0, 8);

        // Store back in session
        session()->put('recently_viewed', $recentlyViewed);
    }

    /**
     * Get recently viewed products
     */
    public static function getRecentlyViewedProducts($limit = 4)
    {
        $recentlyViewedIds = session()->get('recently_viewed', []);

        if (empty($recentlyViewedIds)) {
            return collect();
        }

        // Get products in the order they were viewed
        $products = collect();
        foreach (array_slice($recentlyViewedIds, 0, $limit) as $id) {
            $product = Product::with('category')->find($id);
            if ($product && $product->status === 'active') {
                $products->push($product);
            }
        }

        return $products;
    }
}
