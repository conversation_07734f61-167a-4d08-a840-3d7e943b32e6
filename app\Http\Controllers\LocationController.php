<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LocationController extends Controller
{


    /**
     * Get location by pincode - new method
     */
    public function getPincodeLocation($pincode)
    {
        // Validate the pincode parameter from the route
        if (!$pincode || !preg_match('/^[0-9]{6}$/', $pincode)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid pincode format. Please provide a 6-digit pincode.'
            ], 400);
        }

        // Check cache first
        $cacheKey = "location_pincode_{$pincode}";
        try {
            $cachedLocation = Cache::get($cacheKey);
        } catch (\Exception $e) {
            Log::error("Cache error: " . $e->getMessage());
            $cachedLocation = null;
        }

        if ($cachedLocation) {
            return response()->json([
                'success' => true,
                'data' => $cachedLocation
            ]);
        }

        // Try to get location from external API
        $location = $this->fetchLocationFromAPI($pincode);

        if ($location) {
            // Cache for 24 hours
            try {
                Cache::put($cacheKey, $location, 24 * 60 * 60);
            } catch (\Exception $e) {
                Log::error("Cache put error: " . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'data' => $location
            ]);
        }

        // Fallback to static data for common pincodes
        $staticLocation = $this->getStaticLocationData($pincode);

        if ($staticLocation) {
            // Cache static data for 7 days
            try {
                Cache::put($cacheKey, $staticLocation, 7 * 24 * 60 * 60);
            } catch (\Exception $e) {
                Log::error("Cache put error: " . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'data' => $staticLocation
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Location not found for this pincode'
        ], 404);
    }



    /**
     * Fetch location from external API
     */
    private function fetchLocationFromAPI($pincode)
    {
        try {
            // Using India Post API (free)
            $response = Http::timeout(5)->get("https://api.postalpincode.in/pincode/{$pincode}");

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data[0]['Status']) && $data[0]['Status'] === 'Success' && !empty($data[0]['PostOffice'])) {
                    $postOffice = $data[0]['PostOffice'][0];

                    // Use Circle for state (as requested), fallback to State if Circle is not available
                    $state = $postOffice['Circle'] ?? $postOffice['State'] ?? '';

                    // Clean up state name (remove extra spaces, fix common issues)
                    $state = trim($state);
                    if (strtolower($state) === 'chattisgarh') {
                        $state = 'Chhattisgarh'; // Fix common spelling variation
                    }

                    return [
                        'pincode' => $pincode,
                        'city' => $postOffice['District'] ?? '',
                        'district' => $postOffice['District'] ?? '',
                        'state' => $state,
                        'area' => $postOffice['Name'] ?? '',
                        'country' => 'India'
                    ];
                }
            }
        } catch (\Exception $e) {
            // Log error but don't fail
            Log::warning("Failed to fetch location for pincode {$pincode}: " . $e->getMessage());
        }

        return null;
    }

    /**
     * Get static location data for common pincodes
     */
    private function getStaticLocationData($pincode)
    {
        $staticData = [
            // Mumbai
            '400001' => ['city' => 'Mumbai', 'district' => 'Mumbai', 'state' => 'Maharashtra'],
            '400002' => ['city' => 'Mumbai', 'district' => 'Mumbai', 'state' => 'Maharashtra'],
            '400003' => ['city' => 'Mumbai', 'district' => 'Mumbai', 'state' => 'Maharashtra'],
            '400004' => ['city' => 'Mumbai', 'district' => 'Mumbai', 'state' => 'Maharashtra'],
            '400005' => ['city' => 'Mumbai', 'district' => 'Mumbai', 'state' => 'Maharashtra'],
            
            // Delhi
            '110001' => ['city' => 'New Delhi', 'district' => 'Central Delhi', 'state' => 'Delhi'],
            '110002' => ['city' => 'New Delhi', 'district' => 'Central Delhi', 'state' => 'Delhi'],
            '110003' => ['city' => 'New Delhi', 'district' => 'Central Delhi', 'state' => 'Delhi'],
            '110004' => ['city' => 'New Delhi', 'district' => 'Central Delhi', 'state' => 'Delhi'],
            '110005' => ['city' => 'New Delhi', 'district' => 'Central Delhi', 'state' => 'Delhi'],
            
            // Bangalore
            '560001' => ['city' => 'Bangalore', 'district' => 'Bangalore Urban', 'state' => 'Karnataka'],
            '560002' => ['city' => 'Bangalore', 'district' => 'Bangalore Urban', 'state' => 'Karnataka'],
            '560003' => ['city' => 'Bangalore', 'district' => 'Bangalore Urban', 'state' => 'Karnataka'],
            '560004' => ['city' => 'Bangalore', 'district' => 'Bangalore Urban', 'state' => 'Karnataka'],
            '560005' => ['city' => 'Bangalore', 'district' => 'Bangalore Urban', 'state' => 'Karnataka'],
            
            // Chennai
            '600001' => ['city' => 'Chennai', 'district' => 'Chennai', 'state' => 'Tamil Nadu'],
            '600002' => ['city' => 'Chennai', 'district' => 'Chennai', 'state' => 'Tamil Nadu'],
            '600003' => ['city' => 'Chennai', 'district' => 'Chennai', 'state' => 'Tamil Nadu'],
            '600004' => ['city' => 'Chennai', 'district' => 'Chennai', 'state' => 'Tamil Nadu'],
            '600005' => ['city' => 'Chennai', 'district' => 'Chennai', 'state' => 'Tamil Nadu'],
            
            // Kolkata
            '700001' => ['city' => 'Kolkata', 'district' => 'Kolkata', 'state' => 'West Bengal'],
            '700002' => ['city' => 'Kolkata', 'district' => 'Kolkata', 'state' => 'West Bengal'],
            '700003' => ['city' => 'Kolkata', 'district' => 'Kolkata', 'state' => 'West Bengal'],
            '700004' => ['city' => 'Kolkata', 'district' => 'Kolkata', 'state' => 'West Bengal'],
            '700005' => ['city' => 'Kolkata', 'district' => 'Kolkata', 'state' => 'West Bengal'],
            
            // Hyderabad
            '500001' => ['city' => 'Hyderabad', 'district' => 'Hyderabad', 'state' => 'Telangana'],
            '500002' => ['city' => 'Hyderabad', 'district' => 'Hyderabad', 'state' => 'Telangana'],
            '500003' => ['city' => 'Hyderabad', 'district' => 'Hyderabad', 'state' => 'Telangana'],
            '500004' => ['city' => 'Hyderabad', 'district' => 'Hyderabad', 'state' => 'Telangana'],
            '500005' => ['city' => 'Hyderabad', 'district' => 'Hyderabad', 'state' => 'Telangana'],
            
            // Pune
            '411001' => ['city' => 'Pune', 'district' => 'Pune', 'state' => 'Maharashtra'],
            '411002' => ['city' => 'Pune', 'district' => 'Pune', 'state' => 'Maharashtra'],
            '411003' => ['city' => 'Pune', 'district' => 'Pune', 'state' => 'Maharashtra'],
            '411004' => ['city' => 'Pune', 'district' => 'Pune', 'state' => 'Maharashtra'],
            '411005' => ['city' => 'Pune', 'district' => 'Pune', 'state' => 'Maharashtra'],
            
            // Ahmedabad
            '380001' => ['city' => 'Ahmedabad', 'district' => 'Ahmedabad', 'state' => 'Gujarat'],
            '380002' => ['city' => 'Ahmedabad', 'district' => 'Ahmedabad', 'state' => 'Gujarat'],
            '380003' => ['city' => 'Ahmedabad', 'district' => 'Ahmedabad', 'state' => 'Gujarat'],
            '380004' => ['city' => 'Ahmedabad', 'district' => 'Ahmedabad', 'state' => 'Gujarat'],
            '380005' => ['city' => 'Ahmedabad', 'district' => 'Ahmedabad', 'state' => 'Gujarat'],

            // Raipur, Chhattisgarh
            '492001' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492002' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492003' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492004' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492005' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492006' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492007' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492008' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492009' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
            '492010' => ['city' => 'Raipur', 'district' => 'Raipur', 'state' => 'Chhattisgarh'],
        ];

        if (isset($staticData[$pincode])) {
            return array_merge($staticData[$pincode], [
                'pincode' => $pincode,
                'area' => '',
                'country' => 'India'
            ]);
        }

        return null;
    }

    /**
     * Get all countries
     */
    public function getCountries()
    {
        $countries = [
            'india' => 'India',
            'usa' => 'United States',
            'uk' => 'United Kingdom',
            'canada' => 'Canada',
            'australia' => 'Australia',
            'germany' => 'Germany',
            'france' => 'France',
            'japan' => 'Japan',
            'singapore' => 'Singapore',
            'uae' => 'United Arab Emirates'
        ];

        return response()->json([
            'success' => true,
            'data' => $countries
        ]);
    }

    /**
     * Get all states
     */
    public function getStates()
    {
        $states = [
            'andhra-pradesh' => 'Andhra Pradesh',
            'arunachal-pradesh' => 'Arunachal Pradesh',
            'assam' => 'Assam',
            'bihar' => 'Bihar',
            'chhattisgarh' => 'Chhattisgarh',
            'goa' => 'Goa',
            'gujarat' => 'Gujarat',
            'haryana' => 'Haryana',
            'himachal-pradesh' => 'Himachal Pradesh',
            'jharkhand' => 'Jharkhand',
            'karnataka' => 'Karnataka',
            'kerala' => 'Kerala',
            'madhya-pradesh' => 'Madhya Pradesh',
            'maharashtra' => 'Maharashtra',
            'manipur' => 'Manipur',
            'meghalaya' => 'Meghalaya',
            'mizoram' => 'Mizoram',
            'nagaland' => 'Nagaland',
            'odisha' => 'Odisha',
            'punjab' => 'Punjab',
            'rajasthan' => 'Rajasthan',
            'sikkim' => 'Sikkim',
            'tamil-nadu' => 'Tamil Nadu',
            'telangana' => 'Telangana',
            'tripura' => 'Tripura',
            'uttar-pradesh' => 'Uttar Pradesh',
            'uttarakhand' => 'Uttarakhand',
            'west-bengal' => 'West Bengal',
            'delhi' => 'Delhi',
            'jammu-kashmir' => 'Jammu & Kashmir',
            'ladakh' => 'Ladakh',
            'andaman-nicobar' => 'Andaman & Nicobar Islands',
            'chandigarh' => 'Chandigarh',
            'dadra-nagar-haveli' => 'Dadra & Nagar Haveli',
            'daman-diu' => 'Daman & Diu',
            'lakshadweep' => 'Lakshadweep',
            'puducherry' => 'Puducherry'
        ];

        return response()->json([
            'success' => true,
            'data' => $states
        ]);
    }
}
