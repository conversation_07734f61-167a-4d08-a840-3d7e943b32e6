@extends('layouts.app')

@section('title', $metaTags['title'] ?? $page->title)
@section('description', $metaTags['description'] ?? '')

@if(isset($metaTags['keywords']))
@section('keywords', $metaTags['keywords'])
@endif

@push('meta')
{!! App\Helpers\SeoHelper::renderMetaTags($metaTags) !!}
@endpush

@section('content')
<!-- Page Header -->
<section class="py-5 bg-gradient-pink text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="font-playfair display-4 fw-bold mb-4">{{ $page->title }}</h1>
                @if($page->excerpt)
                <p class="lead mb-0">{{ $page->excerpt }}</p>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Page Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                @if($page->featured_image)
                <div class="text-center mb-5">
                    <img src="{{ $page->featured_image }}" 
                         alt="{{ $page->title }}" 
                         class="img-fluid rounded shadow">
                </div>
                @endif
                
                <div class="content">
                    {!! $page->content !!}
                </div>
                
                @if($page->updated_at)
                <div class="mt-5 pt-4 border-top text-muted small">
                    <div class="row">
                        <div class="col-md-6">
                            <i class="fas fa-calendar me-2"></i>
                            Last updated: {{ $page->updated_at->format('F d, Y') }}
                        </div>
                        @if($page->updater)
                        <div class="col-md-6 text-md-end">
                            <i class="fas fa-user me-2"></i>
                            Updated by: {{ $page->updater->name }}
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Related Pages or Call to Action -->
@if($page->template === 'about' || $page->template === 'contact')
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                @if($page->template === 'about')
                <h3 class="font-playfair mb-4">Explore Our Collections</h3>
                <p class="text-muted mb-4">
                    Discover our exquisite range of jewelry crafted with love and precision.
                </p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ route('collections') }}" class="btn btn-primary-pink">
                        <i class="fas fa-gem me-2"></i>View Collections
                    </a>
                    <a href="{{ route('products') }}" class="btn btn-outline-primary-pink">
                        <i class="fas fa-shopping-bag me-2"></i>Shop Now
                    </a>
                </div>
                @elseif($page->template === 'contact')
                <h3 class="font-playfair mb-4">Get In Touch</h3>
                <p class="text-muted mb-4">
                    We're here to help you find the perfect jewelry for any occasion.
                </p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="tel:+91XXXXXXXXXX" class="btn btn-primary-pink">
                        <i class="fas fa-phone me-2"></i>Call Now
                    </a>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary-pink">
                        <i class="fas fa-envelope me-2"></i>Email Us
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endif

<!-- Structured Data -->
@push('structured-data')
{!! App\Helpers\SeoHelper::renderStructuredData($structuredData) !!}
@endpush
@endsection

@push('styles')
<style>
.content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.content h2 {
    font-family: 'Playfair Display', serif;
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.content h3 {
    font-family: 'Playfair Display', serif;
    color: #34495e;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.content p {
    margin-bottom: 1.25rem;
}

.content ul, .content ol {
    margin-bottom: 1.25rem;
    padding-left: 2rem;
}

.content li {
    margin-bottom: 0.5rem;
}

.content blockquote {
    border-left: 4px solid var(--bs-primary);
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #6c757d;
}

.content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}

.content table {
    width: 100%;
    margin: 1.5rem 0;
    border-collapse: collapse;
}

.content table th,
.content table td {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    text-align: left;
}

.content table th {
    background-color: #f8f9fa;
    font-weight: 600;
}
</style>
@endpush
