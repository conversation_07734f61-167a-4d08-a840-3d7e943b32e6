<?php $__env->startSection('title', 'Promocodes'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row align-items-center mb-4">
        <div class="col-12 col-md-6">
            <h1 class="h4 mb-1 text-gray-800">Promocodes</h1>
            <p class="text-muted mb-0">Manage discount codes and promotional offers</p>
        </div>
        <div class="col-12 col-md-6 mt-2 mt-md-0">
            <div class="d-flex justify-content-md-end">
                <a href="<?php echo e(route('admin.promocodes.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create Promocode
                </a>
            </div>
        </div>
    </div>

    <!-- Promocodes Table -->
    <div class="card shadow">
        <div class="card-body">
            <?php if($promocodes->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Value</th>
                                <th>Usage</th>
                                <th>Status</th>
                                <th>Expires</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $promocodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $promocode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($promocode->code); ?></span>
                                </td>
                                <td><?php echo e($promocode->name); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo e($promocode->type === 'percentage' ? 'success' : 'info'); ?>">
                                        <?php echo e(ucfirst($promocode->type)); ?>

                                    </span>
                                </td>
                                <td><?php echo e($promocode->formatted_value); ?></td>
                                <td>
                                    <?php echo e($promocode->used_count); ?>

                                    <?php if($promocode->usage_limit): ?>
                                        / <?php echo e($promocode->usage_limit); ?>

                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($promocode->is_active && $promocode->isValid()): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php elseif(!$promocode->is_active): ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Expired</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($promocode->expires_at): ?>
                                        <?php echo e($promocode->expires_at->format('M d, Y')); ?>

                                    <?php else: ?>
                                        <span class="text-muted">Never</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.promocodes.show', $promocode)); ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.promocodes.edit', $promocode)); ?>" 
                                           class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.promocodes.destroy', $promocode)); ?>" 
                                              method="POST" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                    onclick="return confirm('Are you sure you want to delete this promocode?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($promocodes->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-tags text-muted mb-4" style="font-size: 4rem;"></i>
                    <h3 class="text-muted mb-3">No Promocodes Found</h3>
                    <p class="text-muted mb-4">Create your first promocode to start offering discounts to customers.</p>
                    <a href="<?php echo e(route('admin.promocodes.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Promocode
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/admin/promocodes/index.blade.php ENDPATH**/ ?>