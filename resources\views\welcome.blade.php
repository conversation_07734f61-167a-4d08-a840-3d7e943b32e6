@extends('layouts.app')

@section('title', 'ShreeJi - Exquisite Jewelry Collection | Premium Gold, Silver & Diamond Jewelry')
@section('description', 'Discover beautiful handcrafted jewelry at ShreeJi. Premium rings, necklaces, earrings, and bracelets with free shipping across India. Shop authentic gold, silver, and diamond jewelry.')
@section('keywords', 'jewelry, rings, necklaces, earrings, bracelets, gold jewelry, silver jewelry, diamond jewelry, handcrafted jewelry, Indian jewelry, wedding jewelry, engagement rings, traditional jewelry, modern jewelry')
@section('og_title', 'ShreeJi - Exquisite Jewelry Collection')
@section('og_description', 'Discover beautiful handcrafted jewelry at ShreeJi. Premium rings, necklaces, earrings, and bracelets with free shipping across India.')
@section('content')

<!-- Hero Section -->
<section id="home" class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="font-modern fw-bold mb-4 brand-text">
                    💎 <span class="font-julius" style="color: var(--primary-brown);">Shreeji <PERSON>g Jewels</span>
                </h1>
                <p class="lead mb-4 text-muted font-body">
                    At Shreeji Marg Jewels, we create handmade fashion jewellery that reflects elegance, emotion, and individuality.
                    Each piece is crafted with care by skilled artisans, using premium, skin-friendly materials — no gold or silver, just pure style and craftsmanship.
                </p>
                <div class="mb-4">
                    <h5 class="font-cursive-bold tagline-text mb-2" style="color: var(--primary-brown);">✨ Handmade. Pure. Trusted.</h5>
                    <p class="text-muted font-body" style="font-style: italic; font-weight: 300;">Your story, our craftsmanship.</p>
                </div>
                <div class="d-flex flex-wrap gap-3">
                    <a href="{{ url('/collections') }}" class="btn font-modern" style="background-color: var(--primary-brown); color: var(--primary-cream); border-radius: 25px; padding: 12px 24px; font-weight: 600;">
                        <i class="fas fa-gem me-2"></i>Shop Collection
                    </a>
                </div>

                <!-- Stats -->
                {{-- <div class="row mt-5">
                    <div class="col-4">
                        <h3 class="font-playfair text-primary-pink mb-0">500+</h3>
                        <small class="text-muted">Happy Customers</small>
                    </div>
                    <div class="col-4">
                        <h3 class="font-playfair text-primary-pink mb-0">50+</h3>
                        <small class="text-muted">Unique Designs</small>
                    </div>
                    <div class="col-4">
                        <h3 class="font-playfair text-primary-pink mb-0">5★</h3>
                        <small class="text-muted">Customer Rating</small>
                    </div>
                </div> --}}
            </div>

            <div class="col-lg-6 mt-3">
                <div class="row g-3">
                    <!-- Feature Cards -->
                    <div class="col-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-shipping-fast fs-4 mb-2" style="color: var(--primary-brown);"></i>
                                <h6 class="mb-1 font-modern">Free Shipping</h6>
                                <small class="text-muted">On orders above ₹999</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-certificate fs-4 mb-2" style="color: var(--primary-brown);"></i>
                                <h6 class="mb-1 font-modern">Certified</h6>
                                <small class="text-muted">Quality Assured</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-hand-sparkles fs-4 mb-2" style="color: var(--primary-brown);"></i>
                                <h6 class="mb-1 font-modern">Handmade</h6>
                                <small class="text-muted">Crafted with Care</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-shield-alt fs-4 mb-2" style="color: var(--primary-brown);"></i>
                                <h6 class="mb-1 font-modern">Trusted</h6>
                                <small class="text-muted">Since 2020</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Collections -->
<section id="collections" class="py-5">
    <div class="container">
        <h2 class="section-title">Featured Collections</h2>

        <div class="row g-3 g-md-4">
            @if(isset($featuredCategories) && $featuredCategories->count() > 0)
                @foreach($featuredCategories as $category)
                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="card product-card h-100 border-0 shadow-sm">
                            <div class="position-relative overflow-hidden">
                                <img src="{{ $category->image_url }}"
                                     class="card-img-top" alt="{{ $category->name }} Collection"
                                     style="height: 250px; object-fit: cover;"
                                     onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'">
                                <div class="product-overlay">
                                    <a href="{{ route('collections.category', $category->slug) }}" class="btn btn-light rounded-pill">
                                        <i class="fas fa-eye me-2"></i>View Collection
                                    </a>
                                </div>
                            </div>
                            <div class="card-body text-center">
                                <h5 class="card-title font-playfair">{{ $category->name }}</h5>
                                <p class="card-text text-muted">{{ $category->description }}</p>
                                <div class="d-flex justify-content-center align-items-center">
                                    @php
                                        $minPrice = $category->products()->active()->inStock()->min('price');
                                        $productCount = $category->products()->active()->inStock()->count();
                                    @endphp
                                    @if($minPrice)
                                        <span class="fw-bold font-modern" style="color: var(--primary-brown);">From ₹{{ number_format($minPrice) }}</span>
                                    @endif
                                    @if($productCount > 0)
                                        <small class="text-muted ms-2">({{ $productCount }} items)</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <!-- Fallback static collections if no featured categories found -->
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="card product-card h-100 border-0 shadow-sm">
                        <div class="position-relative overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                                 class="card-img-top" alt="Rings Collection" style="height: 250px; object-fit: cover;">
                            <div class="product-overlay">
                                <a href="{{ route('collections') }}" class="btn btn-light rounded-pill">
                                    <i class="fas fa-eye me-2"></i>View Collection
                                </a>
                            </div>
                        </div>
                        <div class="card-body text-center">
                            <h5 class="card-title font-playfair">Elegant Rings</h5>
                            <p class="card-text text-muted">Discover our stunning ring collection</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div class="text-center mt-5">
            <a href="{{ route('collections') }}" class="btn btn-outline-pink btn-lg">
                <i class="fas fa-th me-2"></i>View All Collections
            </a>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="section-title">Featured Products</h2>
        <p class="text-center text-muted mb-5">Handpicked pieces from our exclusive collection</p>

        @if(isset($featuredProducts) && $featuredProducts->count() > 0)
            <div class="row g-3 g-md-4">
                @foreach($featuredProducts->take(4) as $product)
                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="card product-card h-100 border-0 shadow-sm">
                            <div class="position-relative overflow-hidden">
                                @if($product->images && count($product->images) > 0)
                                    @php
                                        $imagePath = $product->images[0];
                                        // Check if it's a full URL
                                        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                                            $imageUrl = $imagePath;
                                        } else {
                                            // Clean the path and ensure storage/ prefix
                                            $cleanPath = str_replace(['\\/', '\\'], '/', $imagePath);
                                            // Add storage/ prefix if not already present
                                            if (!str_starts_with($cleanPath, 'storage/')) {
                                                $cleanPath = 'storage/' . ltrim($cleanPath, '/');
                                            }
                                            $imageUrl = asset($cleanPath);
                                        }
                                    @endphp
                                    <img src="{{ $imageUrl }}"
                                         alt="{{ $product->name }}"
                                         class="card-img-top"
                                         style="height: 250px; object-fit: cover;"
                                         onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                                @else
                                    <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                         alt="{{ $product->name }}"
                                         class="card-img-top"
                                         style="height: 250px; object-fit: cover;">
                                @endif

                                <!-- Product badges -->
                                <div class="position-absolute top-0 start-0 p-2">
                                    <span class="badge" style="background-color: var(--primary-brown); color: var(--primary-cream);">Featured</span>
                                    @if($product->isOnSale())
                                        <span class="badge bg-warning ms-1">Sale</span>
                                    @endif
                                </div>

                                <!-- Removed overlay for cleaner design -->
                            </div>

                            <div class="card-body text-center">
                                <div class="mb-2">
                                    <small class="text-muted">{{ $product->category->name }}</small>
                                </div>
                                <h5 class="card-title font-playfair mb-2">{{ $product->name }}</h5>
                                <p class="card-text text-muted small mb-3">{{ Str::limit($product->short_description, 60) }}</p>

                                <div class="d-flex justify-content-center align-items-center mb-3">
                                    @if($product->isOnSale())
                                        <span class="fw-bold me-2 font-modern" style="color: var(--primary-brown);">₹{{ number_format($product->sale_price) }}</span>
                                        <span class="text-muted text-decoration-line-through small">₹{{ number_format($product->price) }}</span>
                                    @else
                                        <span class="fw-bold font-modern" style="color: var(--primary-brown);">₹{{ number_format($product->price) }}</span>
                                    @endif
                                </div>

                                <!-- Price Display -->
                                <div class="mb-3">
                                    @if($product->isOnSale())
                                        <div class="d-flex align-items-center justify-content-center gap-2">
                                            <span class="h5 mb-0 text-success fw-bold">₹{{ number_format($product->sale_price) }}</span>
                                            <small class="text-muted text-decoration-line-through">₹{{ number_format($product->price) }}</small>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <span class="h5 mb-0 fw-bold">₹{{ number_format($product->price) }}</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Add to Cart Button -->
                                @if($product->in_stock)
                                    <button class="btn btn-primary-pink w-100 mb-2 add-to-cart-btn"
                                        data-product-id="{{ $product->id }}" style="font-weight: 600; padding: 12px;">
                                        <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                    </button>
                                    <div class="d-flex gap-2 mb-2">
                                        <a href="{{ route('product.detail', $product->slug) }}"
                                           class="btn btn-outline-primary btn-sm w-100">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                    </div>
                                    <span class="badge bg-success-subtle text-success">In Stock</span>
                                @else
                                    <button class="btn btn-secondary w-100 mb-2" disabled>
                                        <i class="fas fa-times me-2"></i>Out of Stock
                                    </button>
                                    <a href="{{ route('product.detail', $product->slug) }}"
                                       class="btn btn-outline-primary btn-sm w-100 mb-2">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    <span class="badge bg-danger-subtle text-danger">Out of Stock</span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="text-center mt-5">
                <a href="{{ route('collections') }}?sort=featured" class="btn btn-lg font-modern" style="background-color: var(--primary-brown); color: var(--primary-cream); border-radius: 25px; padding: 15px 30px; font-weight: 600;">
                    <i class="fas fa-star me-2"></i>View All Featured Products
                </a>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-gem text-muted mb-3" style="font-size: 3rem;"></i>
                <h4 class="text-muted">No Featured Products Available</h4>
                <p class="text-muted">Check back soon for our handpicked collection!</p>
                <a href="{{ route('collections') }}" class="btn btn-outline-pink">
                    <i class="fas fa-th me-2"></i>Browse All Products
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="section-title">What Our Customers Say</h2>

        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4">"Absolutely stunning jewelry! The quality is exceptional and the designs are so unique. I've received countless compliments on my necklace."</p>
                        <div class="d-flex align-items-center justify-content-center">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3" width="50" height="50">
                            <div>
                                <h6 class="mb-0">Priya Sharma</h6>
                                <small class="text-muted">Mumbai</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4">"ShreeJi has the most beautiful collection I've ever seen. The craftsmanship is incredible and the customer service is outstanding."</p>
                        <div class="d-flex align-items-center justify-content-center">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3" width="50" height="50">
                            <div>
                                <h6 class="mb-0">Anita Patel</h6>
                                <small class="text-muted">Delhi</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4">"Perfect for my wedding! The team helped me choose the most beautiful set. Every piece is a work of art. Highly recommended!"</p>
                        <div class="d-flex align-items-center justify-content-center">
                            <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3" width="50" height="50">
                            <div>
                                <h6 class="mb-0">Kavya Reddy</h6>
                                <small class="text-muted">Bangalore</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
    /* Mobile-First Welcome Page Styles */

    /* Enhanced Typography for Mobile */
    @media (max-width: 576px) {
        .hero-section {
            padding: 2rem 0;
        }

        .hero-section .container {
            padding: 0 1rem;
        }

        .brand-text {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .font-julius {
            font-size: 1.4rem;
            line-height: 1.2;
            display: block;
            margin-top: 0.5rem;
        }

        .tagline-text {
            text-align: center;
            font-size: 0.95rem;
        }

        .lead {
            font-size: 0.9rem;
            line-height: 1.5;
            text-align: center;
        }

        /* .btn {
            width: 100%;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            padding: 12px 20px;
        } */

        .card-body {
            padding: 1rem 0.75rem;
        }

        .card-title {
            font-size: 1rem;
            line-height: 1.3;
        }

        .card-text {
            font-size: 0.85rem;
            line-height: 1.4;
        }
    }

    /* Tablet Styles */
    @media (min-width: 577px) and (max-width: 768px) {
        .font-julius {
            font-size: 1.8rem;
            line-height: 1.2;
        }

        .tagline-text {
            font-size: 1.1rem;
        }

        .lead {
            font-size: 1rem;
            line-height: 1.6;
        }
    }

    /* Desktop Styles */
    @media (min-width: 769px) {
        .font-julius {
            font-size: 2.2rem;
            line-height: 1.1;
        }

        .tagline-text {
            font-size: 1.3rem;
        }

        .lead {
            font-size: 1.1rem;
            line-height: 1.7;
        }
    }

    /* Enhanced Button Styles */
    .btn {
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        border: none;
        box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
    }

    .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 10px rgba(139, 69, 19, 0.2);
    }

    /* Enhanced Card Styles */
    .card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    /* Typography Enhancements */
    .font-julius {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-cursive-bold {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-modern {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-body {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Accessibility Improvements */
    @media (prefers-reduced-motion: reduce) {
        .btn, .card {
            transition: none;
        }

        .btn:hover, .card:hover {
            transform: none;
        }
    }

    /* High DPI Display Optimizations */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .font-julius, .font-cursive-bold, .font-modern, .font-body {
            -webkit-font-smoothing: subpixel-antialiased;
        }
    }
</style>
@endpush

@push('structured-data')
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "ShreeJi Jewelry",
    "alternateName": "Shreeji Marg Jewels",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('images/logo.png') }}",
    "description": "ShreeJi - Exquisite handcrafted jewelry collection. Premium rings, necklaces, earrings, and bracelets with free shipping across India.",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": ["English", "Hindi"]
    },
    "sameAs": [
        "https://www.facebook.com/shreejijewelry",
        "https://www.instagram.com/shreejijewelry",
        "https://www.twitter.com/shreejijewelry"
    ]
}
</script>

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "ShreeJi Jewelry",
    "url": "{{ url('/') }}",
    "description": "Discover beautiful handcrafted jewelry at ShreeJi. Premium rings, necklaces, earrings, and bracelets with free shipping across India.",
    "potentialAction": {
        "@type": "SearchAction",
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": "{{ url('/collections') }}?search={search_term_string}"
        },
        "query-input": "required name=search_term_string"
    }
}
</script>

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Store",
    "name": "ShreeJi Jewelry",
    "image": "{{ asset('images/store-front.jpg') }}",
    "description": "Premium handcrafted jewelry store offering exquisite rings, necklaces, earrings, and bracelets.",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN"
    },
    "geo": {
        "@type": "GeoCoordinates",
        "latitude": "19.0760",
        "longitude": "72.8777"
    },
    "url": "{{ url('/') }}",
    "telephone": "+91-XXXXXXXXXX",
    "priceRange": "₹₹₹",
    "paymentAccepted": ["Cash", "Credit Card", "Debit Card", "UPI", "Net Banking"],
    "currenciesAccepted": "INR",
    "openingHours": "Mo-Sa 10:00-20:00"
}
</script>
@endpush
