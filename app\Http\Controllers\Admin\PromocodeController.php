<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Promocode;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PromocodeController extends Controller
{
    public function index()
    {
        $promocodes = Promocode::orderBy('created_at', 'desc')->paginate(20);
        
        return view('admin.promocodes.index', compact('promocodes'));
    }

    public function create()
    {
        $categories = Category::active()->get();
        $products = Product::active()->get();
        
        return view('admin.promocodes.create', compact('categories', 'products'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'code' => 'required|string|max:50|unique:promocodes,code',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
            'applicable_categories' => 'nullable|array',
            'applicable_products' => 'nullable|array',
        ]);

        $data = $request->all();
        $data['code'] = strtoupper($data['code']);
        $data['is_active'] = $request->has('is_active');
        $data['first_order_only'] = $request->has('first_order_only');
        $data['used_count'] = 0;

        Promocode::create($data);

        return redirect()->route('admin.promocodes.index')
            ->with('success', 'Promocode created successfully!');
    }

    public function show(Promocode $promocode)
    {
        $promocode->load('usages');
        
        return view('admin.promocodes.show', compact('promocode'));
    }

    public function edit(Promocode $promocode)
    {
        $categories = Category::active()->get();
        $products = Product::active()->get();
        
        return view('admin.promocodes.edit', compact('promocode', 'categories', 'products'));
    }

    public function update(Request $request, Promocode $promocode)
    {
        $request->validate([
            'code' => 'required|string|max:50|unique:promocodes,code,' . $promocode->id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
            'applicable_categories' => 'nullable|array',
            'applicable_products' => 'nullable|array',
        ]);

        $data = $request->all();
        $data['code'] = strtoupper($data['code']);
        $data['is_active'] = $request->has('is_active');
        $data['first_order_only'] = $request->has('first_order_only');

        $promocode->update($data);

        return redirect()->route('admin.promocodes.index')
            ->with('success', 'Promocode updated successfully!');
    }

    public function destroy(Promocode $promocode)
    {
        $promocode->delete();

        return redirect()->route('admin.promocodes.index')
            ->with('success', 'Promocode deleted successfully!');
    }
}
