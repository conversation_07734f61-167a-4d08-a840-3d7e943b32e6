<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Homepage
Route::get('/', function () {
    return view('welcome');
})->name('home');
// Remove this route in production - use php artisan storage:link instead


// Collections & Products
Route::get('/products', [App\Http\Controllers\ProductController::class, 'index'])->name('products');
Route::get('/collections', [App\Http\Controllers\ProductController::class, 'index'])->name('collections');
Route::get('/collections/{category}', [App\Http\Controllers\ProductController::class, 'category'])->name('collections.category');
Route::get('/product/{slug}', [App\Http\Controllers\ProductController::class, 'show'])->name('product.detail');
Route::get('/search', [App\Http\Controllers\ProductController::class, 'search'])->name('search');
Route::get('/api/products/featured', [App\Http\Controllers\ProductController::class, 'featured'])->name('api.products.featured');



// SEO Routes
Route::get('/sitemap.xml', [App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', [App\Http\Controllers\SitemapController::class, 'robots'])->name('robots');

// Shopping Cart & Checkout
Route::get('/cart', [App\Http\Controllers\CartController::class, 'index'])->name('cart');
Route::post('/cart/add', [App\Http\Controllers\CartController::class, 'add'])->name('cart.add');
Route::post('/cart/update', [App\Http\Controllers\CartController::class, 'update'])->name('cart.update');
Route::post('/cart/remove', [App\Http\Controllers\CartController::class, 'remove'])->name('cart.remove');

Route::post('/cart/apply-promo', [App\Http\Controllers\CartController::class, 'applyPromo'])->name('cart.apply-promo');
Route::post('/cart/remove-promo', [App\Http\Controllers\CartController::class, 'removePromo'])->name('cart.remove-promo');
Route::delete('/cart', [App\Http\Controllers\CartController::class, 'clear'])->name('cart.clear');
Route::get('/api/cart/count', [App\Http\Controllers\CartController::class, 'count'])->name('api.cart.count');

// Checkout routes (guest-only experience)
Route::get('/checkout', [App\Http\Controllers\OrderController::class, 'checkout'])->name('checkout');
Route::post('/checkout', [App\Http\Controllers\OrderController::class, 'store'])->name('checkout.store');

// Public checkout success page
Route::get('/checkout/success/{order}', [App\Http\Controllers\OrderController::class, 'success'])->name('checkout.success');

// User Orders (public routes for guest and user orders)
Route::get('/orders', [App\Http\Controllers\UserController::class, 'orders'])->name('orders');
Route::get('/order/{id}', [App\Http\Controllers\UserController::class, 'orderDetail'])->name('order.detail');

// Payment Routes (Webhook doesn't need auth)
Route::post('/payment/webhook', [App\Http\Controllers\PaymentController::class, 'webhook'])->name('payment.webhook');
Route::get('/payment/test-details', [App\Http\Controllers\PaymentController::class, 'getTestDetails'])->name('payment.test-details');

// Temporary routes for testing (remove auth requirement)
Route::post('/payment/create-order', [App\Http\Controllers\PaymentController::class, 'createOrder'])->name('payment.create-order.temp');
Route::post('/payment/verify', [App\Http\Controllers\PaymentController::class, 'verifyPayment'])->name('payment.verify.temp');
Route::post('/payment/failed', [App\Http\Controllers\PaymentController::class, 'paymentFailed'])->name('payment.failed.temp');

// Test routes - remove in production
if (config('app.env') === 'local') {
    Route::get('/test-razorpay', function() {
        try {
            return response()->json([
                'status' => 'config_check',
                'razorpay_config' => [
                    'key_id' => config('services.razorpay.key_id'),
                    'key_secret' => config('services.razorpay.key_secret') ? 'SET' : 'NOT SET',
                    'webhook_secret' => config('services.razorpay.webhook_secret') ? 'SET' : 'NOT SET',
                ],
                'message' => 'Razorpay configuration loaded. To test order creation, you need valid test credentials.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    })->name('test.razorpay');
}


// Admin Authentication (Essential for store management)
Route::prefix('admin')->group(function () {
    Route::get('/login', [App\Http\Controllers\AuthController::class, 'showLogin'])->name('admin.login');
    Route::post('/login', [App\Http\Controllers\AuthController::class, 'login'])->name('admin.login.post');
    Route::post('/logout', [App\Http\Controllers\AuthController::class, 'logout'])->name('admin.logout');
});

// Admin Routes (for store management)
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');

    // Product Management
    Route::get('/products', [App\Http\Controllers\Admin\AdminController::class, 'products'])->name('products.index');
    Route::get('/products/create', [App\Http\Controllers\Admin\AdminController::class, 'createProduct'])->name('products.create');
    Route::post('/products', [App\Http\Controllers\Admin\AdminController::class, 'storeProduct'])->name('products.store');
    Route::get('/products/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editProduct'])->name('products.edit');
    Route::put('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateProduct'])->name('products.update');
    Route::delete('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteProduct'])->name('products.delete');

    // Category Management
    Route::get('/categories', [App\Http\Controllers\Admin\AdminController::class, 'categories'])->name('categories.index');
    Route::get('/categories/create', [App\Http\Controllers\Admin\AdminController::class, 'createCategory'])->name('categories.create');
    Route::post('/categories', [App\Http\Controllers\Admin\AdminController::class, 'storeCategory'])->name('categories.store');
    Route::get('/categories/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editCategory'])->name('categories.edit');
    Route::put('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateCategory'])->name('categories.update');
    Route::delete('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteCategory'])->name('categories.delete');

    // Order Management
    Route::get('/orders', [App\Http\Controllers\Admin\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{id}', [App\Http\Controllers\Admin\OrderController::class, 'show'])->name('orders.show');
    Route::post('/orders/{id}/status', [App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::get('/orders/export', [App\Http\Controllers\Admin\OrderController::class, 'export'])->name('orders.export');

    // Order Tracking Routes
    Route::prefix('orders/tracking')->name('orders.tracking.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\OrderTrackingController::class, 'index'])->name('index');
        Route::get('/{id}', [App\Http\Controllers\Admin\OrderTrackingController::class, 'show'])->name('show');
        Route::get('/{id}/info', [App\Http\Controllers\Admin\OrderTrackingController::class, 'getTrackingInfo'])->name('info');
        Route::post('/{id}/status', [App\Http\Controllers\Admin\OrderTrackingController::class, 'updateStatus'])->name('update-status');
        Route::post('/{id}/tracking', [App\Http\Controllers\Admin\OrderTrackingController::class, 'addTracking'])->name('add-tracking');
        Route::post('/bulk-update', [App\Http\Controllers\Admin\OrderTrackingController::class, 'bulkUpdateStatus'])->name('bulk-update');
        Route::get('/export', [App\Http\Controllers\Admin\OrderTrackingController::class, 'exportOrders'])->name('export');
    });

    // Page Management
    Route::resource('pages', App\Http\Controllers\Admin\PageController::class);
    Route::post('/pages/bulk-action', [App\Http\Controllers\Admin\PageController::class, 'bulkAction'])->name('pages.bulk-action');

    // Customer Management
    Route::get('/customers', [App\Http\Controllers\Admin\CustomerController::class, 'index'])->name('customers.index');
    Route::get('/customers/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'show'])->name('customers.show');
    Route::post('/customers/{id}/status', [App\Http\Controllers\Admin\CustomerController::class, 'updateStatus'])->name('customers.update-status');
    Route::post('/customers/{id}/membership', [App\Http\Controllers\Admin\CustomerController::class, 'updateMembership'])->name('customers.update-membership');
    Route::get('/customers/export', [App\Http\Controllers\Admin\CustomerController::class, 'export'])->name('customers.export');

    // Promocode Management
    Route::resource('promocodes', App\Http\Controllers\Admin\PromocodeController::class);

    // Settings Management
    Route::get('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
    Route::get('/settings/shipping', [App\Http\Controllers\Admin\SettingsController::class, 'shipping'])->name('settings.shipping');
    Route::put('/settings/shipping', [App\Http\Controllers\Admin\SettingsController::class, 'updateShipping'])->name('settings.shipping.update');
});

// Payment Routes (now public for guest checkout) - with rate limiting for security
Route::middleware(['throttle:10,1'])->group(function () {
    Route::post('/payment/create-order', [App\Http\Controllers\PaymentController::class, 'createOrder'])->name('payment.create-order');
    Route::post('/payment/verify', [App\Http\Controllers\PaymentController::class, 'verifyPayment'])->name('payment.verify');
    Route::post('/payment/failed', [App\Http\Controllers\PaymentController::class, 'paymentFailed'])->name('payment.failed');
});

// Webhook route without rate limiting (Razorpay needs to call this)
Route::post('/payment/webhook', [App\Http\Controllers\PaymentController::class, 'webhook'])->name('payment.webhook');


// Customer Service Pages - Keep existing routes for backward compatibility
Route::get('/shipping', function () {
    return view('pages.shipping');
})->name('shipping');

Route::get('/returns', function () {
    return view('pages.returns');
})->name('returns');

Route::get('/size-guide', function () {
    return view('pages.size-guide');
})->name('size-guide');

Route::get('/jewelry-care', function () {
    return view('pages.jewelry-care');
})->name('jewelry-care');

Route::get('/warranty', function () {
    return view('pages.warranty');
})->name('warranty');

// Legal Pages - Keep existing routes for backward compatibility
Route::get('/privacy-policy', function () {
    return view('pages.privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
    return view('pages.terms-of-service');
})->name('terms-of-service');

Route::get('/cookie-policy', function () {
    return view('pages.cookie-policy');
})->name('cookie-policy');

// Order Tracking (Public)
Route::get('/track-order', [App\Http\Controllers\OrderTrackingController::class, 'index'])->name('order-tracking.index');
Route::post('/track-order', [App\Http\Controllers\OrderTrackingController::class, 'track'])->name('order-tracking.track');

// Dynamic Pages System
Route::get('/page/{slug}', [App\Http\Controllers\PageController::class, 'show'])->name('page.show');
Route::get('/pages/search', [App\Http\Controllers\PageController::class, 'search'])->name('pages.search');

// Admin Routes (for product management)
Route::prefix('admin')->name('admin.')->middleware('auth')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');

    // Product Management
    Route::get('/products', [App\Http\Controllers\Admin\AdminController::class, 'products'])->name('products.index');
    Route::get('/products/create', [App\Http\Controllers\Admin\AdminController::class, 'createProduct'])->name('products.create');
    Route::post('/products', [App\Http\Controllers\Admin\AdminController::class, 'storeProduct'])->name('products.store');
    Route::get('/products/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editProduct'])->name('products.edit');
    Route::put('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateProduct'])->name('products.update');
    Route::delete('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteProduct'])->name('products.delete');
    Route::post('/products/bulk-delete', [App\Http\Controllers\Admin\AdminController::class, 'bulkDeleteProducts'])->name('products.bulk-delete');


    // Category Management
    Route::get('/categories', [App\Http\Controllers\Admin\AdminController::class, 'categories'])->name('categories.index');
    Route::get('/categories/create', [App\Http\Controllers\Admin\AdminController::class, 'createCategory'])->name('categories.create');
    Route::post('/categories', [App\Http\Controllers\Admin\AdminController::class, 'storeCategory'])->name('categories.store');
    Route::get('/categories/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editCategory'])->name('categories.edit');
    Route::put('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateCategory'])->name('categories.update');
    Route::delete('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteCategory'])->name('categories.delete');

    // Order Management
    Route::get('/orders', function () {
        return view('admin.orders.index');
    })->name('orders.index');

    // Order Tracking Routes
    Route::prefix('orders/tracking')->name('orders.tracking.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\OrderTrackingController::class, 'index'])->name('index');
        Route::get('/{id}', [App\Http\Controllers\Admin\OrderTrackingController::class, 'show'])->name('show');
        Route::get('/{id}/info', [App\Http\Controllers\Admin\OrderTrackingController::class, 'getTrackingInfo'])->name('info');
        Route::post('/{id}/status', [App\Http\Controllers\Admin\OrderTrackingController::class, 'updateStatus'])->name('update-status');
        Route::post('/{id}/tracking', [App\Http\Controllers\Admin\OrderTrackingController::class, 'addTracking'])->name('add-tracking');
        Route::post('/bulk-update', [App\Http\Controllers\Admin\OrderTrackingController::class, 'bulkUpdateStatus'])->name('bulk-update');
        Route::get('/export', [App\Http\Controllers\Admin\OrderTrackingController::class, 'exportOrders'])->name('export');
    });

    // Page Management
    Route::resource('pages', App\Http\Controllers\Admin\PageController::class);
    Route::post('/pages/bulk-action', [App\Http\Controllers\Admin\PageController::class, 'bulkAction'])->name('pages.bulk-action');

    // Customer Management
    Route::get('/customers', function () {
        return view('admin.customers.index');
    })->name('customers.index');

    // Admin Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ProfileController::class, 'show'])->name('show');
        Route::get('/edit', [App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('update');
        Route::put('/password', [App\Http\Controllers\Admin\ProfileController::class, 'updatePassword'])->name('password');
        Route::put('/preferences', [App\Http\Controllers\Admin\ProfileController::class, 'updatePreferences'])->name('preferences');
        Route::get('/activity', [App\Http\Controllers\Admin\ProfileController::class, 'activity'])->name('activity');
    });
});
