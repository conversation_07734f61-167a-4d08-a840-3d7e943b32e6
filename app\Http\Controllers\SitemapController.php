<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SitemapController extends Controller
{
    public function index()
    {
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Homepage
        $sitemap .= $this->addUrl(url('/'), '1.0', 'daily', now()->format('Y-m-d'));

        // Static pages
        $staticPages = [
            'collections' => '0.9',
            'about' => '0.7',
            'contact' => '0.7',
            'shipping' => '0.5',
            'returns' => '0.5',
            'size-guide' => '0.5',
            'jewelry-care' => '0.5',
            'warranty' => '0.5',
        ];

        foreach ($staticPages as $page => $priority) {
            $sitemap .= $this->addUrl(url($page), $priority, 'weekly', now()->format('Y-m-d'));
        }

        // Categories
        $categories = Category::where('is_active', true)->get();
        foreach ($categories as $category) {
            $sitemap .= $this->addUrl(
                route('collections.category', $category->slug),
                '0.8',
                'weekly',
                $category->updated_at->format('Y-m-d')
            );
        }

        // Products
        $products = Product::where('is_active', true)
            ->where('in_stock', true)
            ->orderBy('updated_at', 'desc')
            ->get();

        foreach ($products as $product) {
            $priority = $product->is_featured ? '0.9' : '0.8';
            $sitemap .= $this->addUrl(
                route('product.detail', $product->slug),
                $priority,
                'weekly',
                $product->updated_at->format('Y-m-d')
            );
        }

        $sitemap .= '</urlset>';

        return response($sitemap, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600', // Cache for 1 hour
        ]);
    }

    private function addUrl($url, $priority, $changefreq, $lastmod)
    {
        return "  <url>\n" .
               "    <loc>" . htmlspecialchars($url) . "</loc>\n" .
               "    <lastmod>" . $lastmod . "</lastmod>\n" .
               "    <changefreq>" . $changefreq . "</changefreq>\n" .
               "    <priority>" . $priority . "</priority>\n" .
               "  </url>\n";
    }

    public function robots()
    {
        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /cart/\n";
        $robots .= "Disallow: /checkout/\n";
        $robots .= "Disallow: /account/\n";
        $robots .= "Disallow: /api/\n";
        $robots .= "Disallow: /storage/\n";
        $robots .= "\n";
        $robots .= "Sitemap: " . url('/sitemap.xml') . "\n";

        return response($robots, 200, [
            'Content-Type' => 'text/plain',
        ]);
    }
}
