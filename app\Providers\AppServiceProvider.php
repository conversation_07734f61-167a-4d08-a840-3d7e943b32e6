<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\CartItem;
use App\Models\Category;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share cart count and categories globally
        View::composer('*', function ($view) {
            // Cart count
            $userId = Auth::id();
            $sessionId = Session::getId();
            $cartCount = CartItem::getCartCount($userId, $sessionId);

            // Global categories for navigation
            $globalCategories = Category::active()->orderBy('name')->get();

            $view->with([
                'cartCount' => $cartCount,
                'globalCategories' => $globalCategories
            ]);
        });

        // Register custom Blade directives for contact information
        \Blade::directive('phone', function () {
            return "<?php echo App\Helpers\ContactHelper::phoneFormatted(); ?>";
        });

        \Blade::directive('phoneLink', function () {
            return "<?php echo App\Helpers\ContactHelper::phoneLink(); ?>";
        });

        \Blade::directive('email', function () {
            return "<?php echo App\Helpers\ContactHelper::email(); ?>";
        });

        \Blade::directive('whatsapp', function ($message = '') {
            return "<?php echo App\Helpers\ContactHelper::whatsappLink($message); ?>";
        });

        \Blade::directive('instagram', function () {
            return "<?php echo App\Helpers\ContactHelper::instagramUrl(); ?>";
        });

        \Blade::directive('address', function () {
            return "<?php echo App\Helpers\ContactHelper::address(); ?>";
        });

        \Blade::directive('businessHours', function () {
            return "<?php echo App\Helpers\ContactHelper::businessHoursDisplay(); ?>";
        });
    }
}
