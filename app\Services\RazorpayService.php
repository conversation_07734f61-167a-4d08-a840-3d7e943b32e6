<?php

namespace App\Services;

use Razorpay\Api\Api;
use Razorpay\Api\Errors\BadRequestError;
use Razorpay\Api\Errors\ServerError;
use Razorpay\Api\Errors\SignatureVerificationError;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Exception;

class RazorpayService
{
    protected $api;
    protected $keyId;
    protected $keySecret;
    protected $webhookSecret;

    public function __construct()
    {
        $this->keyId = config('services.razorpay.key_id');
        $this->keySecret = config('services.razorpay.key_secret');
        $this->webhookSecret = config('services.razorpay.webhook_secret');

        // Validate configuration
        if (empty($this->keyId) || empty($this->keySecret)) {
            throw new Exception('Razorpay credentials not configured properly');
        }

        $this->api = new Api($this->keyId, $this->keySecret);
    }

    /**
     * Create a Razorpay order
     */
    public function createOrder(Order $order)
    {
        try {
            Log::info('Creating Razorpay order', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'amount' => $order->total_amount,
                'currency' => $order->currency
            ]);

            // Ensure amount is an integer (in paise)
            $amountInPaise = (int) round($order->total_amount * 100);

            $razorpayOrder = $this->api->order->create([
                'receipt' => $order->order_number,
                'amount' => $amountInPaise, // Amount in paise (must be integer)
                'currency' => $order->currency,
                'notes' => [
                    'order_id' => $order->id,
                    'customer_name' => $order->user->name,
                    'customer_email' => $order->user->email,
                ]
            ]);

            Log::info('Razorpay order created successfully', ['razorpay_order_id' => $razorpayOrder['id']]);

            // Update order with Razorpay order ID
            $order->update([
                'payment_gateway_order_id' => $razorpayOrder['id']
            ]);

            return [
                'success' => true,
                'order_id' => $razorpayOrder['id'],
                'amount' => $razorpayOrder['amount'],
                'currency' => $razorpayOrder['currency'],
                'key_id' => $this->keyId,
            ];

        } catch (BadRequestError $e) {
            Log::error('Razorpay Order Creation Failed - Bad Request: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Invalid request parameters',
                'message' => $e->getMessage()
            ];
        } catch (ServerError $e) {
            Log::error('Razorpay Order Creation Failed - Server Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment gateway server error',
                'message' => 'Please try again later'
            ];
        } catch (Exception $e) {
            Log::error('Razorpay Order Creation Failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment processing failed',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify payment signature
     */
    public function verifyPayment($razorpayOrderId, $razorpayPaymentId, $razorpaySignature)
    {
        try {
            $attributes = [
                'razorpay_order_id' => $razorpayOrderId,
                'razorpay_payment_id' => $razorpayPaymentId,
                'razorpay_signature' => $razorpaySignature
            ];

            $this->api->utility->verifyPaymentSignature($attributes);
            
            return [
                'success' => true,
                'payment_id' => $razorpayPaymentId
            ];

        } catch (SignatureVerificationError $e) {
            Log::error('Razorpay Signature Verification Failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment verification failed',
                'message' => 'Invalid payment signature'
            ];
        } catch (Exception $e) {
            Log::error('Razorpay Payment Verification Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment verification error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Fetch payment details
     */
    public function getPaymentDetails($paymentId)
    {
        try {
            $payment = $this->api->payment->fetch($paymentId);
            
            return [
                'success' => true,
                'payment' => $payment->toArray()
            ];

        } catch (Exception $e) {
            Log::error('Failed to fetch payment details: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Failed to fetch payment details',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Process refund
     */
    public function processRefund($paymentId, $amount = null, $notes = [])
    {
        try {
            $refundData = ['notes' => $notes];

            if ($amount) {
                $refundData['amount'] = (int) round($amount * 100); // Amount in paise (must be integer)
            }

            $refund = $this->api->payment->fetch($paymentId)->refund($refundData);
            
            return [
                'success' => true,
                'refund_id' => $refund['id'],
                'amount' => $refund['amount'] / 100,
                'status' => $refund['status']
            ];

        } catch (Exception $e) {
            Log::error('Razorpay Refund Failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Refund processing failed',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature($payload, $signature)
    {
        try {
            $this->api->utility->verifyWebhookSignature($payload, $signature, $this->webhookSecret);
            return true;
        } catch (SignatureVerificationError $e) {
            Log::error('Webhook signature verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get test card details for testing
     */
    public function getTestCardDetails()
    {
        return [
            'success_cards' => [
                [
                    'number' => '****************',
                    'cvv' => '123',
                    'expiry' => '12/25',
                    'name' => 'Test Card'
                ],
                [
                    'number' => '****************',
                    'cvv' => '123',
                    'expiry' => '12/25',
                    'name' => 'Test Mastercard'
                ]
            ],
            'failure_cards' => [
                [
                    'number' => '****************',
                    'cvv' => '123',
                    'expiry' => '12/25',
                    'name' => 'Declined Card'
                ]
            ],
            'test_upi_id' => 'success@razorpay',
            'test_netbanking' => 'HDFC'
        ];
    }
}
