<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OrderTrackingController extends Controller
{
    /**
     * Show the order tracking page
     */
    public function index()
    {
        return view('order-tracking.index');
    }

    /**
     * Track an order by order number or tracking number
     */
    public function track(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'search' => 'required|string|min:3|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please enter a valid order number or tracking number.',
                'errors' => $validator->errors()
            ], 422);
        }

        $search = trim($request->search);
        
        // Try to find order by order number or tracking number
        $order = Order::with(['user', 'orderItems.product'])
            ->where(function ($query) use ($search) {
                $query->where('order_number', $search)
                      ->orWhere('tracking_number', $search);
            })
            ->first();

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found. Please check your order number or tracking number and try again.'
            ], 404);
        }

        // Return order tracking information
        return response()->json([
            'success' => true,
            'order' => [
                'id' => $order->id,
                'order_number' => $order->order_number,
                'status' => $order->status,
                'status_display' => $order->getStatusDisplayName(),
                'total_amount' => $order->total_amount,
                'currency' => $order->currency,
                'created_at' => $order->created_at->format('M d, Y h:i A'),
                'tracking_number' => $order->tracking_number,
                'courier_service' => $order->courier_service,
                'tracking_url' => $order->tracking_url,
                'customer_name' => $order->billing_address['name'] ?? $order->user->name,
                'customer_phone' => $order->billing_address['phone'] ?? $order->user->phone,
                'shipping_address' => $order->shipping_address,
                'billing_address' => $order->billing_address,
                'items' => $order->orderItems->map(function ($item) {
                    return [
                        'name' => $item->product_name,
                        'sku' => $item->product_sku,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'total' => $item->total,
                        'size' => $item->size,
                        'image' => $item->product->main_image_url ?? null,
                    ];
                }),
                'status_history' => $this->getStatusHistory($order),
                'timestamps' => [
                    'confirmed_at' => $order->confirmed_at?->format('M d, Y h:i A'),
                    'processing_at' => $order->processing_at?->format('M d, Y h:i A'),
                    'packed_at' => $order->packed_at?->format('M d, Y h:i A'),
                    'shipped_at' => $order->shipped_at?->format('M d, Y h:i A'),
                    'out_for_delivery_at' => $order->out_for_delivery_at?->format('M d, Y h:i A'),
                    'delivered_at' => $order->delivered_at?->format('M d, Y h:i A'),
                ]
            ]
        ]);
    }

    /**
     * Get formatted status history for display
     */
    private function getStatusHistory($order)
    {
        $history = [];
        
        // Order placed
        $history[] = [
            'status' => 'pending',
            'title' => 'Order Placed',
            'description' => 'Your order has been placed successfully',
            'timestamp' => $order->created_at->format('M d, Y h:i A'),
            'completed' => true,
            'icon' => 'fas fa-shopping-cart'
        ];

        // Order confirmed
        if ($order->confirmed_at) {
            $history[] = [
                'status' => 'confirmed',
                'title' => 'Order Confirmed',
                'description' => 'Your order has been confirmed and is being prepared',
                'timestamp' => $order->confirmed_at->format('M d, Y h:i A'),
                'completed' => true,
                'icon' => 'fas fa-check-circle'
            ];
        }

        // Processing
        if ($order->processing_at) {
            $history[] = [
                'status' => 'processing',
                'title' => 'Processing',
                'description' => 'Your order is being processed',
                'timestamp' => $order->processing_at->format('M d, Y h:i A'),
                'completed' => true,
                'icon' => 'fas fa-cogs'
            ];
        }

        // Packed
        if ($order->packed_at) {
            $history[] = [
                'status' => 'packed',
                'title' => 'Packed',
                'description' => 'Your order has been packed and ready for shipment',
                'timestamp' => $order->packed_at->format('M d, Y h:i A'),
                'completed' => true,
                'icon' => 'fas fa-box'
            ];
        }

        // Shipped
        if ($order->shipped_at) {
            $history[] = [
                'status' => 'shipped',
                'title' => 'Shipped',
                'description' => $order->tracking_number ? 
                    "Shipped via {$order->courier_service}. Tracking: {$order->tracking_number}" : 
                    'Your order has been shipped',
                'timestamp' => $order->shipped_at->format('M d, Y h:i A'),
                'completed' => true,
                'icon' => 'fas fa-shipping-fast'
            ];
        }

        // Out for delivery
        if ($order->out_for_delivery_at) {
            $history[] = [
                'status' => 'out_for_delivery',
                'title' => 'Out for Delivery',
                'description' => 'Your order is out for delivery',
                'timestamp' => $order->out_for_delivery_at->format('M d, Y h:i A'),
                'completed' => true,
                'icon' => 'fas fa-truck'
            ];
        }

        // Delivered
        if ($order->delivered_at) {
            $history[] = [
                'status' => 'delivered',
                'title' => 'Delivered',
                'description' => $order->delivered_to ? 
                    "Delivered to {$order->delivered_to}" : 
                    'Your order has been delivered successfully',
                'timestamp' => $order->delivered_at->format('M d, Y h:i A'),
                'completed' => true,
                'icon' => 'fas fa-check-double'
            ];
        }

        return $history;
    }
}
