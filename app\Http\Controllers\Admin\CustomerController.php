<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!Auth::user()->isAdmin()) {
                abort(403, 'Access denied. Admin privileges required.');
            }
            return $next($request);
        });
    }

    public function index(Request $request)
    {
        $query = User::where('role', 'customer')
            ->withCount('orders')
            ->withSum('orders', 'total_amount')
            ->orderBy('created_at', 'desc');

        // Filter by search term (name, email, phone)
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('email', 'like', '%' . $searchTerm . '%')
                  ->orWhere('phone', 'like', '%' . $searchTerm . '%');
            });
        }

        // Filter by membership level
        if ($request->filled('membership')) {
            $query->where('membership_level', $request->membership);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status == 'active') {
                $query->where('status', 'active');
            } elseif ($request->status == 'inactive') {
                $query->where('status', 'inactive');
            }
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $customers = $query->paginate(20);

        // Get customer statistics
        $totalCustomers = User::where('role', 'customer')->count();
        $activeCustomers = User::where('role', 'customer')->where('status', 'active')->count();
        $newCustomers = User::where('role', 'customer')->whereDate('created_at', today())->count();
        $vipCustomers = User::where('role', 'customer')->where('membership_level', 'vip')->count();

        return view('admin.customers.index', compact(
            'customers', 
            'totalCustomers', 
            'activeCustomers', 
            'newCustomers', 
            'vipCustomers'
        ));
    }

    public function show($id)
    {
        $customer = User::where('role', 'customer')
            ->with(['orders' => function($query) {
                $query->orderBy('created_at', 'desc');
            }])
            ->withCount('orders')
            ->withSum('orders', 'total_amount')
            ->findOrFail($id);
        
        return view('admin.customers.show', compact('customer'));
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:active,inactive',
        ]);

        $customer = User::where('role', 'customer')->findOrFail($id);
        $customer->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => 'Customer status updated successfully!',
            'customer' => $customer->fresh()
        ]);
    }

    public function updateMembership(Request $request, $id)
    {
        $request->validate([
            'membership_level' => 'required|in:regular,premium,vip',
        ]);

        $customer = User::where('role', 'customer')->findOrFail($id);
        $customer->update(['membership_level' => $request->membership_level]);

        return response()->json([
            'success' => true,
            'message' => 'Customer membership updated successfully!',
            'customer' => $customer->fresh()
        ]);
    }

    public function export(Request $request)
    {
        $query = User::where('role', 'customer')
            ->withCount('orders')
            ->withSum('orders', 'total_amount');

        // Apply same filters as index
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('email', 'like', '%' . $searchTerm . '%')
                  ->orWhere('phone', 'like', '%' . $searchTerm . '%');
            });
        }
        if ($request->filled('membership')) {
            $query->where('membership_level', $request->membership);
        }
        if ($request->filled('status')) {
            if ($request->status == 'active') {
                $query->where('status', 'active');
            } elseif ($request->status == 'inactive') {
                $query->where('status', 'inactive');
            }
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $customers = $query->get();

        $filename = 'customers_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($customers) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Name', 'Email', 'Phone', 'Membership Level', 'Status', 
                'Total Spent', 'Orders Count', 'Joined Date'
            ]);

            // CSV data
            foreach ($customers as $customer) {
                fputcsv($file, [
                    $customer->name,
                    $customer->email,
                    $customer->phone ?? 'N/A',
                    ucfirst($customer->membership_level ?? 'regular'),
                    ucfirst($customer->status ?? 'active'),
                    '₹' . number_format($customer->orders_sum_total_amount ?? 0, 2),
                    $customer->orders_count ?? 0,
                    $customer->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
