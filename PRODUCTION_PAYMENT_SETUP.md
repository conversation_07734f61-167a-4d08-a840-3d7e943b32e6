# Production Payment Setup Guide

## Razorpay Production Configuration

### 1. Create Razorpay Live Account
1. Go to [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Complete KYC verification
3. Activate your live account
4. Get your live API keys

### 2. Update Environment Variables
Replace test credentials with live credentials in your `.env` file:

```env
# Razorpay Live Configuration
RAZORPAY_KEY_ID=rzp_live_YOUR_LIVE_KEY_ID
RAZORPAY_KEY_SECRET=YOUR_LIVE_KEY_SECRET
RAZORPAY_WEBHOOK_SECRET=YOUR_LIVE_WEBHOOK_SECRET

# Set to production
APP_ENV=production
APP_DEBUG=false
```

### 3. Webhook Configuration
1. In Razorpay Dashboard, go to Settings > Webhooks
2. Add webhook URL: `https://yourdomain.com/payment/webhook`
3. Select events:
   - `payment.authorized`
   - `payment.captured`
   - `payment.failed`
   - `order.paid`
4. Copy the webhook secret to your `.env` file

### 4. Payment Methods Configuration
The current implementation supports:
- Credit/Debit Cards (Visa, Mastercard, Amex, RuPay)
- Net Banking (All major banks)
- UPI (Google Pay, PhonePe, Paytm, etc.)
- Wallets (Paytm, Mobikwik, Freecharge, etc.)
- EMI options
- International cards (if enabled)

### 5. Security Enhancements

#### SSL Certificate
Ensure your website has a valid SSL certificate for secure payments.

#### CSRF Protection
Already implemented in the checkout form.

#### Payment Signature Verification
Already implemented in `RazorpayService::verifyPayment()`.

#### Rate Limiting
Consider adding rate limiting to payment endpoints:

```php
Route::middleware(['throttle:10,1'])->group(function () {
    Route::post('/payment/initialize', [PaymentController::class, 'initialize']);
    Route::post('/payment/verify', [PaymentController::class, 'verify']);
});
```

### 6. Error Handling & Logging

#### Payment Failure Handling
The system already handles:
- Network failures
- Invalid payment details
- Insufficient funds
- Card declined
- Bank server errors

#### Logging
All payment activities are logged in:
- `storage/logs/laravel.log`
- Razorpay Dashboard

### 7. Testing in Production

#### Test Transactions
Use these test card numbers in live mode (small amounts):
- Success: ************** 1111
- Failure: ************** 0002

#### Monitoring
Monitor these metrics:
- Payment success rate
- Average transaction time
- Failed payment reasons
- Webhook delivery status

### 8. Compliance & Regulations

#### PCI DSS Compliance
Razorpay is PCI DSS Level 1 compliant. Your application doesn't store card data.

#### RBI Guidelines
- All transactions are processed through RBI-approved payment aggregators
- Customer data is encrypted and secure
- Transaction limits as per RBI guidelines

### 9. Customer Support

#### Payment Issues
Common issues and solutions:
1. **Payment stuck**: Check webhook delivery
2. **Double charging**: Implement idempotency keys
3. **Refund requests**: Use Razorpay refund API

#### Contact Information
Provide customers with:
- Support email: <EMAIL>
- Support phone: +91-XXXXXXXXXX
- Business hours: 9 AM - 6 PM IST

### 10. Backup Payment Methods

Consider adding alternative payment methods:
- Cash on Delivery (already implemented)
- Bank transfer/NEFT
- Cryptocurrency (if applicable)

### 11. Performance Optimization

#### Caching
- Cache Razorpay configuration
- Use Redis for session management
- Implement database query optimization

#### CDN
Use CDN for Razorpay JavaScript files for faster loading.

### 12. Maintenance

#### Regular Updates
- Update Razorpay PHP SDK regularly
- Monitor Laravel security updates
- Review payment logs monthly

#### Backup Strategy
- Daily database backups
- Transaction data retention policy
- Disaster recovery plan

## Production Checklist

- [ ] Live Razorpay account activated
- [ ] KYC verification completed
- [ ] Live API keys configured
- [ ] Webhook endpoints configured
- [ ] SSL certificate installed
- [ ] Payment flow tested
- [ ] Error handling verified
- [ ] Logging configured
- [ ] Customer support ready
- [ ] Compliance documentation ready
- [ ] Backup systems in place
- [ ] Monitoring tools configured

## Support Contacts

- **Razorpay Support**: <EMAIL>
- **Technical Issues**: +91-80-6190-6200
- **Integration Help**: https://razorpay.com/docs/

## Emergency Procedures

### Payment Gateway Down
1. Enable "Cash on Delivery" only mode
2. Display maintenance message
3. Contact Razorpay support
4. Monitor status page: https://status.razorpay.com/

### Security Incident
1. Immediately disable payment processing
2. Contact Razorpay security team
3. Review transaction logs
4. Implement additional security measures
5. Notify customers if required

## Performance Benchmarks

### Expected Metrics
- Payment success rate: >95%
- Average transaction time: <30 seconds
- Webhook delivery: <5 seconds
- Page load time: <3 seconds

### Monitoring Tools
- Razorpay Dashboard
- Laravel Telescope (for debugging)
- Application Performance Monitoring (APM)
- Server monitoring tools
