@extends('layouts.admin')

@section('title', 'Promocodes')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row align-items-center mb-4">
        <div class="col-12 col-md-6">
            <h1 class="h4 mb-1 text-gray-800">Promocodes</h1>
            <p class="text-muted mb-0">Manage discount codes and promotional offers</p>
        </div>
        <div class="col-12 col-md-6 mt-2 mt-md-0">
            <div class="d-flex justify-content-md-end">
                <a href="{{ route('admin.promocodes.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create Promocode
                </a>
            </div>
        </div>
    </div>

    <!-- Promocodes Table -->
    <div class="card shadow">
        <div class="card-body">
            @if($promocodes->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Value</th>
                                <th>Usage</th>
                                <th>Status</th>
                                <th>Expires</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($promocodes as $promocode)
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ $promocode->code }}</span>
                                </td>
                                <td>{{ $promocode->name }}</td>
                                <td>
                                    <span class="badge bg-{{ $promocode->type === 'percentage' ? 'success' : 'info' }}">
                                        {{ ucfirst($promocode->type) }}
                                    </span>
                                </td>
                                <td>{{ $promocode->formatted_value }}</td>
                                <td>
                                    {{ $promocode->used_count }}
                                    @if($promocode->usage_limit)
                                        / {{ $promocode->usage_limit }}
                                    @endif
                                </td>
                                <td>
                                    @if($promocode->is_active && $promocode->isValid())
                                        <span class="badge bg-success">Active</span>
                                    @elseif(!$promocode->is_active)
                                        <span class="badge bg-secondary">Inactive</span>
                                    @else
                                        <span class="badge bg-danger">Expired</span>
                                    @endif
                                </td>
                                <td>
                                    @if($promocode->expires_at)
                                        {{ $promocode->expires_at->format('M d, Y') }}
                                    @else
                                        <span class="text-muted">Never</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.promocodes.show', $promocode) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.promocodes.edit', $promocode) }}" 
                                           class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.promocodes.destroy', $promocode) }}" 
                                              method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                    onclick="return confirm('Are you sure you want to delete this promocode?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $promocodes->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-tags text-muted mb-4" style="font-size: 4rem;"></i>
                    <h3 class="text-muted mb-3">No Promocodes Found</h3>
                    <p class="text-muted mb-4">Create your first promocode to start offering discounts to customers.</p>
                    <a href="{{ route('admin.promocodes.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Promocode
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
