@extends('layouts.admin')

@section('title', 'Shipping Settings')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row align-items-center mb-4">
        <div class="col-12 col-md-6">
            <h1 class="h4 mb-1 text-gray-800">Shipping Settings</h1>
            <p class="text-muted mb-0">Manage shipping charges and delivery options</p>
        </div>
        <div class="col-12 col-md-6 mt-2 mt-md-0">
            <div class="d-flex justify-content-md-end">
                <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>All Settings
                </a>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Shipping Configuration</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.settings.shipping.update') }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="shipping_charge" class="form-label">Standard Shipping Charge (₹) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('shipping_charge') is-invalid @enderror" 
                                       id="shipping_charge" name="shipping_charge" 
                                       value="{{ old('shipping_charge', \App\Models\Setting::get('shipping_charge', 500)) }}" 
                                       step="0.01" min="0" required>
                                @error('shipping_charge')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Standard shipping charge for all orders</small>
                            </div>
                            <div class="col-md-6">
                                <label for="free_shipping_threshold" class="form-label">Free Shipping Threshold (₹) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('free_shipping_threshold') is-invalid @enderror" 
                                       id="free_shipping_threshold" name="free_shipping_threshold" 
                                       value="{{ old('free_shipping_threshold', \App\Models\Setting::get('free_shipping_threshold', 25000)) }}" 
                                       step="0.01" min="0" required>
                                @error('free_shipping_threshold')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Minimum order amount for free shipping</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="express_shipping_charge" class="form-label">Express Shipping Charge (₹) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('express_shipping_charge') is-invalid @enderror" 
                                       id="express_shipping_charge" name="express_shipping_charge" 
                                       value="{{ old('express_shipping_charge', \App\Models\Setting::get('express_shipping_charge', 1000)) }}" 
                                       step="0.01" min="0" required>
                                @error('express_shipping_charge')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Express delivery charge (1-2 days)</small>
                            </div>
                            <div class="col-md-6">
                                <label for="cod_charge" class="form-label">Cash on Delivery Charge (₹) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('cod_charge') is-invalid @enderror" 
                                       id="cod_charge" name="cod_charge" 
                                       value="{{ old('cod_charge', \App\Models\Setting::get('cod_charge', 50)) }}" 
                                       step="0.01" min="0" required>
                                @error('cod_charge')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Additional charge for cash on delivery</small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Shipping Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Current Settings</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Standard Shipping:</strong><br>
                        <span class="text-success">₹{{ number_format(\App\Models\Setting::get('shipping_charge', 500)) }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Free Shipping Above:</strong><br>
                        <span class="text-success">₹{{ number_format(\App\Models\Setting::get('free_shipping_threshold', 25000)) }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Express Shipping:</strong><br>
                        <span class="text-warning">₹{{ number_format(\App\Models\Setting::get('express_shipping_charge', 1000)) }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>COD Charge:</strong><br>
                        <span class="text-info">₹{{ number_format(\App\Models\Setting::get('cod_charge', 50)) }}</span>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Shipping Info</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-truck text-primary me-2"></i>
                            Standard delivery: 3-5 business days
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-shipping-fast text-warning me-2"></i>
                            Express delivery: 1-2 business days
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-gift text-success me-2"></i>
                            Free shipping on orders above threshold
                        </li>
                        <li>
                            <i class="fas fa-money-bill text-info me-2"></i>
                            COD available with additional charge
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
