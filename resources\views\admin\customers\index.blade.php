@extends('layouts.admin')

@section('title', 'Manage Customers - Admin - ShreeJi Jewelry')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Customer Management</h1>
        <p class="mb-0 text-muted">Manage customer accounts and relationships</p>
    </div>
    <div>
        <button class="btn btn-primary-pink">
            <i class="fas fa-user-plus me-2"></i>Add Customer
        </button>
    </div>
</div>

<!-- Quick Stats -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ $totalCustomers }}</h4>
                        <p class="text-muted mb-0">Total Customers</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ $vipCustomers }}</h4>
                        <p class="text-muted mb-0">VIP Members</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ $newCustomers }}</h4>
                        <p class="text-muted mb-0">New Today</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ $activeCustomers }}</h4>
                        <p class="text-muted mb-0">Active</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.customers.index') }}">
            <div class="row g-3 align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search"
                               placeholder="Search customers..." value="{{ request('search') }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="membership">
                        <option value="">All Members</option>
                        <option value="regular" {{ request('membership') == 'regular' ? 'selected' : '' }}>Regular</option>
                        <option value="premium" {{ request('membership') == 'premium' ? 'selected' : '' }}>Premium</option>
                        <option value="vip" {{ request('membership') == 'vip' ? 'selected' : '' }}>VIP</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-primary w-100" type="submit">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                </div>
                <div class="col-md-2">
                    <a href="{{ route('admin.customers.export', request()->query()) }}"
                       class="btn btn-outline-success w-100" title="Export">
                        <i class="fas fa-download me-1"></i>Export
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Customers</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>Export
            </button>
            <button class="btn btn-outline-primary btn-sm">
                <i class="fas fa-sync me-1"></i>Refresh
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Customer</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Membership</th>
                        <th>Total Spent</th>
                        <th>Orders</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($customers as $customer)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary-pink text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 40px; height: 40px;">
                                    {{ strtoupper(substr($customer->name, 0, 1)) }}
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ $customer->name }}</h6>
                                    <small class="text-muted">Customer ID: #C{{ str_pad($customer->id, 3, '0', STR_PAD_LEFT) }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ $customer->email }}</td>
                        <td>{{ $customer->phone ?? 'N/A' }}</td>
                        <td>
                            <span class="badge bg-{{
                                $customer->membership_level == 'vip' ? 'warning' :
                                ($customer->membership_level == 'premium' ? 'info' : 'secondary')
                            }}">{{ ucfirst($customer->membership_level ?? 'regular') }}</span>
                        </td>
                        <td><strong>₹{{ number_format($customer->orders_sum_total_amount ?? 0, 0) }}</strong></td>
                        <td>{{ $customer->orders_count ?? 0 }}</td>
                        <td>{{ $customer->created_at->format('M d, Y') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.customers.show', $customer->id) }}"
                                   class="btn btn-outline-primary btn-sm" title="View Profile">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn btn-outline-success btn-sm" title="Update Status"
                                        onclick="showStatusModal({{ $customer->id }}, '{{ $customer->status ?? 'active' }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <a href="{{ route('admin.orders.index', ['search' => $customer->email]) }}"
                                   class="btn btn-outline-info btn-sm" title="View Orders">
                                    <i class="fas fa-shopping-bag"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-users text-muted me-2"></i>
                            <span class="text-muted">No customers found.</span>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="card-footer bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="text-muted mb-0">
                    Showing {{ $customers->firstItem() ?? 0 }} to {{ $customers->lastItem() ?? 0 }} of {{ $customers->total() }} customers
                </p>
            </div>
            <div class="col-md-6">
                {{ $customers->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
