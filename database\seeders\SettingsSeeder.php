<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Shipping Settings
            [
                'key' => 'shipping_charge',
                'value' => '500',
                'type' => 'number',
                'group' => 'shipping',
                'label' => 'Standard Shipping Charge',
                'description' => 'Standard shipping charge in rupees'
            ],
            [
                'key' => 'free_shipping_threshold',
                'value' => '25000',
                'type' => 'number',
                'group' => 'shipping',
                'label' => 'Free Shipping Threshold',
                'description' => 'Minimum order amount for free shipping in rupees'
            ],
            [
                'key' => 'express_shipping_charge',
                'value' => '1000',
                'type' => 'number',
                'group' => 'shipping',
                'label' => 'Express Shipping Charge',
                'description' => 'Express shipping charge in rupees'
            ],
            [
                'key' => 'cod_charge',
                'value' => '50',
                'type' => 'number',
                'group' => 'shipping',
                'label' => 'Cash on Delivery Charge',
                'description' => 'Additional charge for cash on delivery in rupees'
            ],

            // Tax Settings
            [
                'key' => 'gst_rate',
                'value' => '0.03',
                'type' => 'number',
                'group' => 'tax',
                'label' => 'GST Rate',
                'description' => 'GST rate as decimal (0.03 = 3%)'
            ],

            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Shreeji Marg',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'Name of the website'
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Contact Email',
                'description' => 'Primary contact email address'
            ],
            [
                'key' => 'contact_phone',
                'value' => '+91-9876543210',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Contact Phone',
                'description' => 'Primary contact phone number'
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
