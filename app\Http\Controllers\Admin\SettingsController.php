<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    public function index()
    {
        $settings = Setting::getAllGrouped();
        
        return view('admin.settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'nullable'
        ]);

        foreach ($request->settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();
            
            if ($setting) {
                // Cast value based on type
                if ($setting->type === 'boolean') {
                    $value = $request->has("settings.{$key}") ? '1' : '0';
                } elseif ($setting->type === 'number') {
                    $value = is_numeric($value) ? $value : '0';
                }
                
                $setting->update(['value' => $value]);
            }
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully!');
    }

    public function shipping()
    {
        $shippingSettings = Setting::where('group', 'shipping')->get();
        
        return view('admin.settings.shipping', compact('shippingSettings'));
    }

    public function updateShipping(Request $request)
    {
        $request->validate([
            'shipping_charge' => 'required|numeric|min:0',
            'free_shipping_threshold' => 'required|numeric|min:0',
            'express_shipping_charge' => 'required|numeric|min:0',
            'cod_charge' => 'required|numeric|min:0',
        ]);

        Setting::set('shipping_charge', $request->shipping_charge, 'number', 'shipping', 'Standard Shipping Charge', 'Standard shipping charge in rupees');
        Setting::set('free_shipping_threshold', $request->free_shipping_threshold, 'number', 'shipping', 'Free Shipping Threshold', 'Minimum order amount for free shipping in rupees');
        Setting::set('express_shipping_charge', $request->express_shipping_charge, 'number', 'shipping', 'Express Shipping Charge', 'Express shipping charge in rupees');
        Setting::set('cod_charge', $request->cod_charge, 'number', 'shipping', 'Cash on Delivery Charge', 'Additional charge for cash on delivery in rupees');

        return redirect()->route('admin.settings.shipping')
            ->with('success', 'Shipping settings updated successfully!');
    }
}
